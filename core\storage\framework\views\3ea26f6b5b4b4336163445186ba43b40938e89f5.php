<div class="header bg--dark">
    <div class="container">
        <div class="header-bottom">
            <div class="header-bottom-area align-items-center">
                <div class="logo">
                    <a href="<?php echo e(route('home')); ?>">
                        <img src="<?php echo e(getImage(getFilePath('logoIcon') . '/logo.png')); ?>" alt="<?php echo app('translator')->get('logo'); ?>">
                    </a>
                </div>
                <ul class="menu">
                    <li>
                        <a href="<?php echo e(route('home')); ?>"><?php echo app('translator')->get('Home'); ?></a>
                    </li>
                    <li>
                        <a href="#0"><?php echo app('translator')->get('Store'); ?></a>
                        <ul class="sub-menu">
                            <?php if(@$serviceCategories->first()): ?>
                                <li>
                                    <a href="<?php echo e(route('service.category', [@$serviceCategories->first()->slug, 'all='])); ?>"><?php echo app('translator')->get('Browse All'); ?></a>
                                </li>
                            <?php endif; ?>
                            <?php $__currentLoopData = $serviceCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $serviceCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li>
                                    <a href="<?php echo e(route('service.category', $serviceCategory->slug)); ?>">
                                        <?php echo e(__($serviceCategory->name)); ?>

                                    </a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a href="<?php echo e(route('register.domain')); ?>"><?php echo app('translator')->get('Register New Domain'); ?></a>
                            </li>
                        </ul>
                    </li>

                    <?php
                        $pages = App\Models\Page::where('tempname', $activeTemplate)
                            ->where('is_default', 0)
                            ->get();
                    ?>

                    <?php $__currentLoopData = $pages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li>
                            <a href="<?php echo e(route('pages', [$data->slug])); ?>"><?php echo e(__($data->name)); ?></a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <li>
                        <a href="<?php echo e(route('blogs')); ?>"><?php echo app('translator')->get('Announcements'); ?></a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('contact')); ?>"><?php echo app('translator')->get('Contact'); ?></a>
                    </li>

                    <?php if(auth()->guard()->check()): ?>
                        <div class="header-buttons d-flex flex-wrap ms-xl-4 ms-0">
                            <li class="menu-btn">
                                <a href="<?php echo e(route('user.home')); ?>" class="text--white ps-2 d-inline-block"> <i class="las la-home"></i> <?php echo app('translator')->get('Dashboard'); ?></a>
                            </li>
                            <li class="menu-btn ms-xl-2">
                                <a href="<?php echo e(route('user.logout')); ?>" class="btn--base-outline me-xl-2 ms-xl-0 ms-2 ps-2 d-inline-block"> <i class="las la-sign-out-alt"></i> <?php echo app('translator')->get('Logout'); ?></a>
                            </li>
                        </div>
                    <?php else: ?>
                        <li class="menu-btn">
                            <a href="<?php echo e(route('user.login')); ?>" class="text--white ps-2 d-inline-block"> <i class="las la-sign-in-alt"></i> <?php echo app('translator')->get('Login'); ?></a>
                        </li>
                    <?php endif; ?>

                </ul>
                <div class="d-flex align-items-center ms-xl-2 ms-auto me-xl-0 me-2">
                    <?php echo $__env->make($activeTemplate . 'partials.cart_widget', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php if (isset($component)) { $__componentOriginalc42c9808c3bf01021fc91c40d7fd767e12b42d83 = $component; } ?>
<?php $component = App\View\Components\Language::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('language'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Language::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc42c9808c3bf01021fc91c40d7fd767e12b42d83)): ?>
<?php $component = $__componentOriginalc42c9808c3bf01021fc91c40d7fd767e12b42d83; ?>
<?php unset($__componentOriginalc42c9808c3bf01021fc91c40d7fd767e12b42d83); ?>
<?php endif; ?>
                </div>
                <div class="header-trigger-wrapper d-flex d-xl-none align-items-center">
                    <div class="header-trigger">
                        <div class="header-trigger__icon"> <i class="las la-bars"></i></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/partials/header.blade.php ENDPATH**/ ?>