
<?php $__env->startSection('panel'); ?>
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10 ">
            <div class="card-body p-0">
                <div class="table-responsive--md  table-responsive">
                    <table class="table table--light style--two">
                        <thead>
                        <tr>
                            <th><?php echo app('translator')->get('User'); ?></th>
                            <th><?php echo app('translator')->get('Service/Product'); ?></th>
                            <th><?php echo app('translator')->get('Pricing'); ?></th> 
                            <th><?php echo app('translator')->get('Next Due Date'); ?></th>
                            <th><?php echo app('translator')->get('Status'); ?></th>
                            <th><?php echo app('translator')->get('Action'); ?></th>
                        </tr> 
                        </thead> 
                        <tbody>  
                            <?php $__empty_1 = true; $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <span class="fw-bold"><?php echo e($service->user->fullname); ?></span>
                                        <br>
                                        <span class="small">
                                            <a href="<?php echo e(permit('admin.users.detail') ? route('admin.users.detail', $service->user_id) : 'javascript:void(0)'); ?>">
                                                <span>@</span><?php echo e($service->user->username); ?>

                                            </a>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-bold"><?php echo e(__(@$service->product->name)); ?></span>
                                        <br>
                                        <span class="small">
                                            <?php echo e(__(@$service->product->serviceCategory->name)); ?>

                                        </span>
                                    </td>  
                                    <td>
                                        <span class="fw-bold">
                                            <?php echo e($general->cur_sym); ?><?php echo e(getAmount($service->recurring_amount)); ?> <?php echo e(__($general->text)); ?>

                                            <?php echo e(@billingCycle(@$service->billing_cycle, true)['showText']); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php if($service->billing_cycle != 0): ?>
                                            <?php echo e(showDateTime($service->next_due_date, 'd/m/Y')); ?>

                                        <?php else: ?> 
                                            <?php echo app('translator')->get('N/A'); ?>
                                        <?php endif; ?> 
                                    </td>
                                    <td>
                                        <?php echo $service->showStatus; ?>
                                    </td> 
                                    <td>
                                        <?php $hasPermission = App\Models\Role::hasPermission('admin.order.hosting.details')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                            <a href="<?php echo e(route('admin.order.hosting.details', $service->id)); ?>" class="btn btn-sm btn-outline--primary">
                                                <i class="las la-desktop text--shadow"></i> <?php echo app('translator')->get('Details'); ?>
                                            </a>
                                        <?php else: ?>
                                            <button class="btn btn-sm btn-outline--primary" disabled>
                                                <i class="las la-desktop text--shadow"></i> <?php echo app('translator')->get('Details'); ?>
                                            </button>
                                        <?php endif ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>  
                                <tr> 
                                    <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage)); ?></td>
                                </tr>
                            <?php endif; ?>
                            </tbody>
                    </table><!-- table end -->
                </div>
            </div>
            <?php if($services->hasPages()): ?>
                <div class="card-footer py-4">
                    <?php echo e(paginateLinks($services)); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php if(request()->routeIs('admin.services')): ?>
    <?php $__env->startPush('breadcrumb-plugins'); ?>
        <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.search-form','data' => ['placeholder' => 'Username / Email']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('search-form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['placeholder' => 'Username / Email']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
    <?php $__env->stopPush(); ?>
<?php endif; ?>
<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/services.blade.php ENDPATH**/ ?>