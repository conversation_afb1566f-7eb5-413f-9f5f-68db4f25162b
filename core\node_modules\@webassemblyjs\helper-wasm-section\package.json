{"name": "@webassemblyjs/helper-wasm-section", "version": "1.11.1", "description": "", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1"}, "devDependencies": {"@webassemblyjs/wasm-parser": "1.11.1"}, "gitHead": "3f07e2db2031afe0ce686630418c542938c1674b"}