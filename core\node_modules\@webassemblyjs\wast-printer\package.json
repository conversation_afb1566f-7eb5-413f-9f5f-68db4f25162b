{"name": "@webassemblyjs/wast-printer", "version": "1.11.1", "description": "WebAssembly text format printer", "main": "lib/index.js", "module": "esm/index.js", "keywords": ["webassembly", "javascript", "ast", "compiler", "printer", "wast"], "scripts": {"test": "mocha"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@xtuc/long": "4.2.2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.11.1", "@webassemblyjs/wast-parser": "1.11.1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "gitHead": "3f07e2db2031afe0ce686630418c542938c1674b"}