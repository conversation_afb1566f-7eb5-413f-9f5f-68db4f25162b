<?php
    
    if (request()->routeIs('home')) {
        $blogs = @getContent('blog.element', limit: 3);
    } else {
        $blogs = App\Models\Frontend::where('data_keys', 'blog.element')
            ->orderBy('id', 'DESC')
            ->paginate(getPaginate());
    }
    
?>

<div class="contact-section section-full pt-60 pb-60 bg--light">
    <div class="container">
        <div class="card custom--card">
            <div class="card-body">
                <div class="announcements">

                    <?php $__empty_1 = true; $__currentLoopData = $blogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="announcement mb-4 border-bottom pb-4">
                            <h3>
                                <a href="<?php echo e(route('blog.details', [slug(@$blog->data_values->title), @$blog->id])); ?>">
                                    <?php echo e(strLimit(__($blog->data_values->title), 80)); ?>

                                </a>
                            </h3>
                            <ul class="list-inline">
                                <li class="list-inline-item text-muted pr-3">
                                    <i class="far fa-calendar-alt fa-fw"></i>
                                    <?php echo e(showDateTime(@$blog->created_at, 'd F Y')); ?>

                                </li>
                            </ul>
                            <article class="mt-3">
                                <?php echo strLimit(strip_tags($blog->data_values->description), 500) ?>
                            </article>
                            <a href="<?php echo e(route('blog.details', [slug(@$blog->data_values->title), @$blog->id])); ?>" class="btn btn--base btn--sm">
                                <?php echo app('translator')->get('Continue reading'); ?> <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <?php if (isset($component)) { $__componentOriginal5ee599e831bdc2bb496580d5cedaf21b3c64fbde = $component; } ?>
<?php $component = App\View\Components\EmptyMessage::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('empty-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\EmptyMessage::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['div' => ''.e(true).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5ee599e831bdc2bb496580d5cedaf21b3c64fbde)): ?>
<?php $component = $__componentOriginal5ee599e831bdc2bb496580d5cedaf21b3c64fbde; ?>
<?php unset($__componentOriginal5ee599e831bdc2bb496580d5cedaf21b3c64fbde); ?>
<?php endif; ?>
                    <?php endif; ?>

                    <?php if(!request()->routeIs('home')): ?>
                        <?php echo e(paginateLinks($blogs)); ?>

                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/sections/blog.blade.php ENDPATH**/ ?>