<?php $__env->startSection('panel'); ?>
    <div class="row mb-none-30">
        <div class="col-lg-8 col-md-12 mb-30">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 col-sm-12">
                            <div class="form-group position-relative mb-0">
                                <div class="system-search-icon"><i class="las la-search"></i></div>
                                <input class="form-control systemSearch" type="text" name="systemSearch" placeholder="<?php echo app('translator')->get('Search'); ?>...">
                                <div class="system-search-icon-reset"><i class="las la-times"></i></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

           
            <div class="row gy-4">
                <div class="emptyArea"></div>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.setting.index')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.setting.index')); ?>" class="item-link"></a>
                            <i class="las la-cog overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-cog"></i>
                            </div>
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('General Settings'); ?></h3>
                                <p><?php echo app('translator')->get('General settings and configuration'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.setting.system.configuration')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.setting.system.configuration')); ?>" class="item-link"></a>
                            <i class="las la-cogs overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-cogs"></i>
                            </div>
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('System Configuration'); ?></h3>
                                <p><?php echo app('translator')->get('System settings and configuration'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.billing.setting')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.billing.setting')); ?>" class="item-link"></a>
                            <i class="las la-file-alt overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-file-alt"></i>
                            </div>
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Billing Settings'); ?></h3>
                                <p><?php echo app('translator')->get('Manage billing setting'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.service.category')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.service.category')); ?>" class="item-link"></a>
                            <i class="fab fa-servicestack overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="fab fa-servicestack"></i>
                            </div>
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Service Categories'); ?></h3>
                                <p><?php echo app('translator')->get('Manage categories'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.configurable.groups')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.configurable.groups')); ?>" class="item-link"></a>
                            <i class="las la-sliders-h overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-sliders-h"></i>
                            </div>
                            <div class="widget-two__content"> 
                                <h3><?php echo app('translator')->get('Configuration'); ?></h3>
                                <p><?php echo app('translator')->get('Manage extras and options for products'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.gateway.automatic.index')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.gateway.automatic.index')); ?>" class="item-link"></a>
                            <i class="las la-university overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-university"></i> 
                            </div> 
                            <div class="widget-two__content"> 
                                <h3><?php echo app('translator')->get('Payment Gateways'); ?></h3>
                                <p><?php echo app('translator')->get('Setup and manage payment gateways'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.groups.server')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.groups.server')); ?>" class="item-link"></a>
                            <i class="las la-server overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-server"></i>
                            </div>
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Server Groups'); ?></h3>
                                <p><?php echo app('translator')->get('Manage server groups'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.servers')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.servers')); ?>" class="item-link"></a>
                            <i class="las la-server overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-server"></i>
                            </div>
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Servers'); ?></h3>
                                <p><?php echo app('translator')->get('Configure and manage your servers'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.tld')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.tld')); ?>" class="item-link"></a>
                            <i class="las la-table overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-table"></i>
                            </div>
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Domain Pricing/TLDS'); ?></h3>
                                <p><?php echo app('translator')->get('Setup domain extensions and pricing'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?> 

                <?php $hasPermission = App\Models\Role::hasPermission('admin.register.domain')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.register.domain')); ?>" class="item-link"></a>
                            <i class="las la-globe overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-globe"></i>
                            </div>
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Domain Registers'); ?></h3>
                                <p><?php echo app('translator')->get('Configure and manage registers'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.products')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.products')); ?>" class="item-link"></a>
                            <i class="las la-cube overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-cube"></i>
                            </div>
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Products/Services'); ?></h3>
                                <p><?php echo app('translator')->get('Setup and manage products'); ?></p>
                            </div> 
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.coupons')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.coupons')); ?>" class="item-link"></a>
                            <i class="las la-ticket-alt overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-ticket-alt"></i>
                            </div> 
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Coupon'); ?></h3>
                                <p><?php echo app('translator')->get('Setup and manage coupn codes'); ?></p>
                            </div>
                        </div> 
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.orders')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.orders')); ?>" class="item-link"></a>
                            <i class="las la-shopping-cart overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-shopping-cart"></i>
                            </div> 
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Orders'); ?></h3>
                                <p><?php echo app('translator')->get('Manage orders'); ?></p>
                            </div>
                        </div> 
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.invoices')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.invoices')); ?>" class="item-link"></a>
                            <i class="las la-file-alt overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-file-alt"></i>
                            </div> 
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Invoices'); ?></h3>
                                <p><?php echo app('translator')->get('Manage invoices'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.setting.logo.icon')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.setting.logo.icon')); ?>" class="item-link"></a>
                            <i class="las la-images overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-images"></i>
                            </div> 
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Logo & Favicon'); ?></h3>
                                <p><?php echo app('translator')->get('Site icons and logo upload here'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.extensions.index')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.extensions.index')); ?>" class="item-link"></a>
                            <i class="las la-cogs overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-cogs"></i>
                            </div> 
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Extensions'); ?></h3>
                                <p><?php echo app('translator')->get('Configure and manage extensions'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.language.manage')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.language.manage')); ?>" class="item-link"></a>
                            <i class="las la-language overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-language"></i>
                            </div> 
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Language'); ?></h3>
                                <p><?php echo app('translator')->get('Configure site language'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.seo')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.seo')); ?>" class="item-link"></a>
                            <i class="las la-globe-americas overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-globe-americas"></i>
                            </div> 
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('SEO Manager'); ?></h3>
                                <p><?php echo app('translator')->get('Setup meta keywords, description and social title for SEO'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.kyc.setting')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.kyc.setting')); ?>" class="item-link"></a>
                            <i class="las la-user-check overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-user-check"></i>
                            </div> 
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('KYC Setting'); ?></h3>
                                <p><?php echo app('translator')->get('Setup KYC setting to know your customer and verify'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.subscriber.index')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.subscriber.index')); ?>" class="item-link"></a>
                            <i class="las la-thumbs-up overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-thumbs-up"></i>
                            </div> 
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Subscribers'); ?></h3>
                                <p><?php echo app('translator')->get('View all subscribers '); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.maintenance.mode')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.maintenance.mode')); ?>" class="item-link"></a>
                            <i class="las la-robot overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-robot"></i>
                            </div> 
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('Maintenance Mode'); ?></h3>
                                <p><?php echo app('translator')->get('Website in under construction mode'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.setting.cookie')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.setting.cookie')); ?>" class="item-link"></a>
                            <i class="las la-cookie-bite overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-cookie-bite"></i>
                            </div> 
                            <div class="widget-two__content">
                                <h3><?php echo app('translator')->get('GDPR Cookie'); ?></h3>
                                <p><?php echo app('translator')->get('The General Data Protection Regulation (GDPR) is the toughest privacy and security law in the world'); ?></p>
                            </div>
                        </div>
                    </div> 
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.setting.custom.css')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.setting.custom.css')); ?>" class="item-link"></a>
                            <i class="fab fa-css3-alt overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="fab fa-css3-alt"></i>
                            </div> 
                            <div class="widget-two__content"> 
                                <h3><?php echo app('translator')->get('Custom CSS'); ?></h3>
                                <p><?php echo app('translator')->get('Add your own CSS code here to customize the layout of your site'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.system.info')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.system.info')); ?>" class="item-link"></a>
                            <i class="las la-server overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-server"></i>
                            </div> 
                            <div class="widget-two__content"> 
                                <h3><?php echo app('translator')->get('System Information'); ?></h3>
                                <p><?php echo app('translator')->get('This page can show you every detail about the configuration of your laravel website'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.setting.notification.global')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.setting.notification.global')); ?>" class="item-link"></a>
                            <i class="las la-bell overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-bell"></i>
                            </div> 
                            <div class="widget-two__content"> 
                                <h3><?php echo app('translator')->get('Notification Setting'); ?></h3>
                                <p><?php echo app('translator')->get('Configure notification setting'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.cancel.requests')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.cancel.requests')); ?>" class="item-link"></a>
                            <i class="las la-ban overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-ban"></i>
                            </div> 
                            <div class="widget-two__content"> 
                                <h3><?php echo app('translator')->get('Cancellation Requests'); ?></h3>
                                <p><?php echo app('translator')->get('Manage cancellation requests'); ?></p>
                            </div>
                        </div>
                    </div> 
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.automation.errors')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.automation.errors')); ?>" class="item-link"></a>
                            <i class="las la-exclamation-triangle overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-exclamation-triangle"></i>
                            </div> 
                            <div class="widget-two__content"> 
                                <h3><?php echo app('translator')->get('Automation Errors'); ?></h3>
                                <p><?php echo app('translator')->get('Failed module actions'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.request.report')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.request.report')); ?>" class="item-link"></a>
                            <i class="las la-bug overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-bug"></i>
                            </div> 
                            <div class="widget-two__content"> 
                                <h3><?php echo app('translator')->get('Report & Request'); ?></h3>
                                <p><?php echo app('translator')->get('Submit your report and request'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.domains')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.domains')); ?>" class="item-link"></a>
                            <i class="las la-globe overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-globe"></i>
                            </div> 
                            <div class="widget-two__content"> 
                                <h3><?php echo app('translator')->get('All Domains'); ?></h3>
                                <p><?php echo app('translator')->get('Manage Domains'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.services')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.services')); ?>" class="item-link"></a>
                            <i class="las la-server overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-server"></i>
                            </div> 
                            <div class="widget-two__content"> 
                                <h3><?php echo app('translator')->get('All Services'); ?></h3>
                                <p><?php echo app('translator')->get('Manage Services'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.cron.index')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <div class="col-xxl-6 col-sm-6 settingItems" data-setting="Invoices">
                        <div class="widget-two box--shadow2 b-radius--5 bg--white has-link">
                            <a href="<?php echo e(route('admin.cron.index')); ?>" class="item-link"></a>
                            <i class="las la-clock overlay-icon text--success"></i>
                            <div class="widget-two__icon b-radius--5 bg--primary">
                                <i class="las la-clock"></i>
                            </div> 
                            <div class="widget-two__content"> 
                                <h3><?php echo app('translator')->get('Cron Job Setting'); ?></h3>
                                <p><?php echo app('translator')->get('Manage Cron Job'); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif ?>
            </div>
        </div>

        <div class="col-lg-4 col-md-12 mb-30">
            <div class="card bg--dark setupWrapper">
                <div class="card-header d-flex justify-content-between flex-wrap align-items-center">
                    <h5 class="text--white"><?php echo app('translator')->get('Setup'); ?></h5>
                    <small><?php echo e(array_sum($completed)); ?> <?php echo app('translator')->get('of'); ?> <span class="totalCompletedSetup text--white"></span> <?php echo app('translator')->get('Completed'); ?> </small>
                </div>
                <div class="card-body">
                    <div class="row">  
                        <div class="col-md-12 col-sm-12">
                            <ul class="ul-border setup">
                                <li class="text-dot">
                                    <i class="las la-<?php echo e($completed['name_and_logo'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                    <a href="<?php echo e(permit('admin.setting.index') ? route('admin.setting.index') : 'javascript:void(0)'); ?>"><?php echo app('translator')->get('Set company name'); ?> </a> <?php echo app('translator')->get('and'); ?>
                                    <a href="<?php echo e(permit('admin.setting.logo.icon') ? route('admin.setting.logo.icon') : 'javascript:void(0)'); ?>"><?php echo app('translator')->get('logo'); ?></a>
                                </li>
                                <li class="mt-2 text-dot">
                                    <i class="las la-<?php echo e(@$completed['cron'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                    <a href="javascript:void(0)" class="cronModalBtn"><?php echo app('translator')->get('Setup cron automation tasks'); ?> </a>
                                </li>
                                <li class="mt-2 text-dot">
                                    <i class="las la-<?php echo e(@$completed['domain_setup'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                    <a href="<?php echo e(permit('admin.tld') ? route('admin.tld') : 'javascript:void(0)'); ?>">
                                        <?php echo app('translator')->get('Manage domain/TLD setup'); ?> 
                                    </a>
                                </li>
                                <li class="mt-2 text-dot">
                                    <i class="las la-<?php echo e(@$completed['domain_register'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                    <a href="<?php echo e(permit('admin.register.domain') ? route('admin.register.domain') : 'javascript:void(0)'); ?>">
                                        <?php echo app('translator')->get('Activate your first domain register'); ?> 
                                    </a>
                                </li>  
                                <li class="mt-2 text-dot">
                                    <i class="las la-<?php echo e(@$completed['configurable_group'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                    <a href="<?php echo e(permit('admin.configurable.groups') ? route('admin.configurable.groups') : 'javascript:void(0)'); ?>">
                                        <?php echo app('translator')->get('Set configurable group'); ?> 
                                    </a>
                                </li>
                                <li class="mt-2 text-dot"> 
                                    <i class="las la-<?php echo e(@$completed['product'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                    <a href="<?php echo e(permit('admin.products') ? route('admin.products') : 'javascript:void(0)'); ?>">
                                        <?php echo app('translator')->get('Create your first product'); ?> 
                                    </a>
                                </li>
                                <li class="mt-2 text-dot"> 
                                    <i class="las la-<?php echo e(@$completed['setup_gateway'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                    <a href="<?php echo e(permit('admin.gateway.automatic.index') ? route('admin.gateway.automatic.index') : 'javascript:void(0)'); ?>">
                                        <?php echo app('translator')->get('Activate/add your first payment gateway'); ?> 
                                    </a>
                                </li>
                                <li class="mt-2 text-dot">
                                    <i class="las la-<?php echo e(@$completed['service_category'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                    <a href="<?php echo e(permit('admin.service.category') ? route('admin.service.category') : 'javascript:void(0)'); ?>">
                                        <?php echo app('translator')->get('Create first service category'); ?> 
                                    </a>
                                </li>
                                <li class="mt-2 text-dot">
                                    <i class="las la-<?php echo e(@$completed['server_group'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                    <a href="<?php echo e(permit('admin.groups.server') ? route('admin.groups.server') : 'javascript:void(0)'); ?>">
                                        <?php echo app('translator')->get('Create server group'); ?> 
                                    </a>
                                </li>
                                <li class="mt-2 text-dot">
                                    <i class="las la-<?php echo e(@$completed['server'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                    <a href="<?php echo e(permit('admin.servers') ? route('admin.servers') : 'javascript:void(0)'); ?>">
                                        <?php echo app('translator')->get('Setup at least one server'); ?> 
                                    </a>
                                </li>
                                <li class="mt-2 text-dot">
                                    <i class="las la-<?php echo e(@$completed['billing_setting'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                    <a href="<?php echo e(permit('admin.billing.setting') ? route('admin.billing.setting') : 'javascript:void(0)'); ?>">
                                        <?php echo app('translator')->get('Setup invoice generation days'); ?> 
                                    </a>
                                </li>
                                <li class="mt-2 text-dot">
                                    <i class="las la-<?php echo e(@$completed['defaultDomainRegister'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                    <a href="<?php echo e(permit('admin.register.domain') ? route('admin.register.domain') : 'javascript:void(0)'); ?>">
                                        <?php echo app('translator')->get('Setup default domain register for domain availability'); ?> 
                                    </a>
                                </li> 
                                <?php if(isSuperAdmin()): ?>
                                    <li class="mt-2 text-dot">
                                        <i class="las la-<?php echo e(@$completed['admin_profile_setup'] ? 'check text--success' : 'times text--danger'); ?>"></i> 
                                        <a href="<?php echo e(route('admin.profile')); ?>"><?php echo app('translator')->get('Setup profile information for Namecheap'); ?> </a>
                                    </li> 
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

<?php echo $__env->make('admin.partials.cron_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
<script>
    (function($) {
        "use strict";

        $('.cronModalBtn').on('click', function(){
            $('#cronModal').modal('show');
        });

        var systemSearch = $('.systemSearch');
        var settingItems = $('.settingItems');
        var systemSearchActive = $('.system-search-icon-reset');

        var emptyArea = $('.emptyArea');
        var emptyHtml = `<div class="col-xxl-12 col-sm-12 settingItems text-center mt-4">
                            <div class="widget-two box--shadow2 b-radius--5 bg--white">
                                <div class="widget-two__content">
                                    <p><?php echo app('translator')->get('No search results found'); ?></p>
                                </div>
                            </div>
                        </div>`;

        systemSearch.on('keyup', function(){

            var searchInput = $(this).val().toLowerCase();
            var empty = true;
            toogleSystemSearch(searchInput);

            settingItems.filter(function (idx, elem){

                if( $(elem).find('.widget-two__content h3').text().trim().toLowerCase().indexOf(searchInput) >= 0 ){
                    $(elem).show();
                    emptyArea.empty();
                    empty = false;
                }else{
                    $(elem).hide();
                }

            }).sort();

            if(empty){
                emptyArea.html(emptyHtml);
            }
         
        });

        $('.system-search-icon-reset').on('click', function(){
            var input = systemSearch.val(null);
            toogleSystemSearch(null);
            emptyArea.empty();
            settingItems.show();
        });
        
        function toogleSystemSearch(input){
            if(input){
                systemSearchActive.addClass('active');
            }else{
                systemSearchActive.removeClass('active');
            }
        }

        $('.totalCompletedSetup').text($('.setup li').length);

    })(jQuery);
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('style'); ?>
<style>
    .setupWrapper{
        position: sticky;
        top: 40px;
    }
    .system-search-icon {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        aspect-ratio: 1;
        padding: 5px;
        display: grid;
        place-items: center;
        color: #888;
    }
    .system-search-icon ~ .form-control {
        padding-left: 45px;
    }
    .system-search-icon-reset {
        position: absolute;
        right: 0px;
        top: 0;
        height: 100%;
        aspect-ratio: 1;
        display: grid;
        place-items: center;
        color: #888;
        visibility: hidden;
        opacity: 0;
        cursor: pointer;
    }
    .system-search-icon-reset.active{
        visibility: visible;
        opacity: 1;
    }
    .ul-border li, .ul-border li a{
        color: #ffffff;
    }
    .ul-border li a:hover{
        color: #ffffff;
        text-decoration: underline;
    }
    .ul-border li:not(:last-child){
        border-bottom: 1px dotted #ffffff4a;
        padding-bottom: 30px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/setting/system.blade.php ENDPATH**/ ?>