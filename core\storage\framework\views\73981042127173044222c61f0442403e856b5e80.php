<?php
    $footer = @getContent('footer.content', true);
    $policyPages = @getContent('policy_pages.element', orderById:true);
?> 

<!-- Footer Section -->
<footer class="py-4 footer bg-dark-two">
    <div class="container">
        <div class="footer-content text-center">
            <a href="<?php echo e(route('home')); ?>" class="logo mb-3">
                <img src="<?php echo e(getImage(getFilePath('logoIcon') .'/logo.png')); ?>" alt="<?php echo app('translator')->get('logo'); ?>">
            </a>
            <p class="footer-text mx-auto">
                <?php echo e(__(@$footer->data_values->description)); ?>

            </p>
            <ul class="footer-links d-flex flex-wrap gap-3 justify-content-center mt-3 mb-3">
                <li><a href="<?php echo e(route('home')); ?>" class="anchor-decoration text--base"><?php echo app('translator')->get('Home'); ?></a></li>

                <li><a href="<?php echo e(route('blogs')); ?>" class="anchor-decoration text--base"><?php echo app('translator')->get('Announcements'); ?></a></li>
                <?php $__currentLoopData = $policyPages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $policyPage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li>
                        <a href="<?php echo e(route('policy.pages', ['slug'=>slug($policyPage->data_values->title), 'id'=>$policyPage->id])); ?>" 
                            class="anchor-decoration text--base"
                        >
                            <?php echo e(__(@$policyPage->data_values->title)); ?>

                        </a>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <li><a href="<?php echo e(route('contact')); ?>" class="anchor-decoration text--base"><?php echo app('translator')->get('Contact'); ?></a></li>

                <li><a href="<?php echo e(route('user.login')); ?>" class="anchor-decoration text--base"><?php echo app('translator')->get('Login'); ?></a></li>
                <li><a href="<?php echo e(route('user.register')); ?>" class="anchor-decoration text--base"><?php echo app('translator')->get('Register'); ?></a></li>
            </ul>
            <p><?php echo e(__($general->site_name)); ?> &copy; <?php echo e(date('Y')); ?>. <?php echo app('translator')->get('All Rights Reserved'); ?></p>
        </div>
    </div>
</footer>
<?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/partials/footer.blade.php ENDPATH**/ ?>