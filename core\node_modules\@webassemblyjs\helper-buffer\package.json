{"name": "@webassemblyjs/helper-buffer", "version": "1.11.1", "description": "Buffer manipulation utility", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@webassemblyjs/wasm-parser": "1.11.1", "jest-diff": "^24.0.0"}, "gitHead": "3f07e2db2031afe0ce686630418c542938c1674b"}