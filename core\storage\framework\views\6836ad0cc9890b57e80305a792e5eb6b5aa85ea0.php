<?php $__env->startSection('content'); ?>
<div class="pt-60 pb-60 bg--light section-full">
    <div class="container">
          
        <div class="mb-4">
            <form action=""> 
                <div class="d-flex flex-wrap gap-4">
                    <div class="flex-grow-1 form-group"> 
                        <label class="label"><?php echo app('translator')->get('Transaction Number'); ?></label>
                        <input type="text" name="search" value="<?php echo e(request()->search); ?>" class="form-control form--control h-45">
                    </div>
                    <div class="flex-grow-1 form-group"> 
                        <label><?php echo app('translator')->get('Type'); ?></label>
                        <select name="trx_type" class="form-select form--control h-45"> 
                            <option value=""><?php echo app('translator')->get('All'); ?></option>
                            <option value="+" <?php if(request()->trx_type == '+'): echo 'selected'; endif; ?>><?php echo app('translator')->get('Plus'); ?></option>
                            <option value="-" <?php if(request()->trx_type == '-'): echo 'selected'; endif; ?>><?php echo app('translator')->get('Minus'); ?></option>
                        </select> 
                    </div>
                    <div class="flex-grow-1 form-group"> 
                        <label><?php echo app('translator')->get('Remark'); ?></label> 
                        <select class="form-select form--control h-45" name="remark">
                            <option value=""><?php echo app('translator')->get('Any'); ?></option>
                            <?php $__currentLoopData = $remarks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $remark): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($remark->remark); ?>" <?php if(request()->remark == $remark->remark): echo 'selected'; endif; ?>>
                                    <?php echo e(__(keyToTitle($remark->remark))); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> 
                        </select>
                    </div> 
                    <div class="flex-grow-1 form-group align-self-end"> 
                        <button class="btn btn--base w-100"><i class="las la-filter"></i> <?php echo app('translator')->get('Filter'); ?></button>
                    </div>
                </div>  
            </form>
        </div>
        
        <table class="table table--responsive--lg">
            <thead>
                <tr>
                    <th><?php echo app('translator')->get('Trx'); ?></th>
                    <th><?php echo app('translator')->get('Transacted'); ?></th>
                    <th><?php echo app('translator')->get('Amount'); ?></th>
                    <th><?php echo app('translator')->get('Post Balance'); ?></th>
                    <th><?php echo app('translator')->get('Details'); ?></th>
                </tr> 
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $trx): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <?php echo e($trx->trx); ?>

                        </td>
                        <td>
                            <div>
                                <?php echo e(showDateTime($trx->created_at, 'M d, Y, h:i a')); ?>

                                <br>
                                <?php echo e(diffForHumans($trx->created_at)); ?>

                            </div>
                        </td>
                        <td>
                            <span class="fw-bold text--<?php echo e($trx->trx_type == '+' ? 'success' : 'danger'); ?>">
                                <?php echo e($trx->trx_type); ?><?php echo e(showAmount($trx->amount)); ?> <?php echo e(__($general->cur_text)); ?>

                            </span>
                        </td>
                        <td>
                            <?php echo e(showAmount($trx->post_balance)); ?> <?php echo e(__($general->cur_text)); ?>

                        </td>
                        <td>
                            <?php echo e(__($trx->details)); ?>

                        </td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <?php if (isset($component)) { $__componentOriginal5ee599e831bdc2bb496580d5cedaf21b3c64fbde = $component; } ?>
<?php $component = App\View\Components\EmptyMessage::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('empty-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\EmptyMessage::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['table' => '{{','true' => true]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5ee599e831bdc2bb496580d5cedaf21b3c64fbde)): ?>
<?php $component = $__componentOriginal5ee599e831bdc2bb496580d5cedaf21b3c64fbde; ?>
<?php unset($__componentOriginal5ee599e831bdc2bb496580d5cedaf21b3c64fbde); ?>
<?php endif; ?>
                <?php endif; ?>
            </tbody>
        </table>
        <?php if($transactions->hasPages()): ?>
            <div class="mt-5">
                <?php echo e(paginateLinks($transactions)); ?>

            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate.'layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/user/transactions.blade.php ENDPATH**/ ?>