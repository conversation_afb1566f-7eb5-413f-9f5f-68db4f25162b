{"name": "std-env", "version": "3.0.1", "description": "Detect current Javascript environment", "repository": "unjs/std-env", "license": "MIT", "sideEffects": false, "exports": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"prepack": "unbuild", "release": "yarn test && standard-version && git push --follow-tags && npm publish", "test": "node test.cjs"}, "devDependencies": {"standard-version": "^9.3.2", "unbuild": "^0.5.11"}}