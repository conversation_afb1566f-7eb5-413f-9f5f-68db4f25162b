<?php $__env->startSection('panel'); ?>
    <div class="row">
        <div class="col-lg-12">
            <div class="card b-radius--10 ">
                <div class="card-body p-0">
                    <div class="table-responsive--md  table-responsive">
                        <table class="table table--light style--two">
                            <thead>
                            <tr>
                                <th><?php echo app('translator')->get('Client'); ?></th>  
                                <th><?php echo app('translator')->get('Total Service'); ?></th>
                                <th><?php echo app('translator')->get('Total Domain'); ?></th>
                                <th><?php echo app('translator')->get('Email-Phone'); ?></th>
                                <th><?php echo app('translator')->get('Country'); ?></th>
                                <th><?php echo app('translator')->get('Joined At'); ?></th>
                                <th><?php echo app('translator')->get('Balance'); ?></th>
                                <th><?php echo app('translator')->get('Action'); ?></th>
                            </tr> 
                            </thead>
                            <tbody> 
                            <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr> 
                                <td>
                                    <span class="fw-bold"><?php echo e($user->fullname); ?></span>
                                    <br>
                                    <span class="small">
                                        <a href="<?php echo e(permit('admin.users.detail') ? route('admin.users.detail', $user->id) : 'javascript:void(0)'); ?>">
                                            <span>@</span><?php echo e($user->username); ?>

                                        </a>
                                    </span>
                                </td>

                                <td>
                                    <a class="fw-bold" href="<?php echo e(permit('admin.users.services') ? route('admin.users.services', $user->id) : 'javascript:void(0)'); ?>">
                                        <?php echo e($user->hostings->count()); ?>

                                    </a>
                                </td>

                                <td>
                                    <a class="fw-bold" href="<?php echo e(permit('admin.users.domains') ? route('admin.users.domains', $user->id) : 'javascript:void(0)'); ?>">
                                        <?php echo e($user->domains->count()); ?>

                                    </a>
                                </td>

                                <td>
                                    <?php echo e($user->email); ?><br><?php echo e($user->mobile); ?>

                                </td>
                                <td>
                                    <span class="fw-bold" title="<?php echo e(@$user->address->country); ?>"><?php echo e($user->country_code); ?></span>
                                </td>



                                <td>
                                    <?php echo e(showDateTime($user->created_at)); ?> <br> <?php echo e(diffForHumans($user->created_at)); ?>

                                </td>


                                <td>
                                    <span class="fw-bold">

                                    <?php echo e($general->cur_sym); ?><?php echo e(showAmount($user->balance)); ?>

                                    </span>
                                </td>

                                <td>
                                    <div class="button--group">
                                        <?php $hasPermission = App\Models\Role::hasPermission('admin.users.detail')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                            <a href="<?php echo e(route('admin.users.detail', $user->id)); ?>" class="btn btn-sm btn-outline--primary">
                                                <i class="las la-desktop"></i> <?php echo app('translator')->get('Details'); ?>
                                            </a>
                                        <?php else: ?> 
                                            <button class="btn btn-sm btn-outline--primary" disabled>
                                                <i class="las la-desktop"></i> <?php echo app('translator')->get('Details'); ?>
                                            </button>
                                        <?php endif ?>
                                        <?php if(request()->routeIs('admin.users.kyc.pending')): ?>
                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.users.kyc.details')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <a href="<?php echo e(route('admin.users.kyc.details', $user->id)); ?>" class="btn btn-sm btn-outline--dark" target="_blank">
                                                    <i class="las la-user-check"></i><?php echo app('translator')->get('KYC Data'); ?>
                                                </a>
                                            <?php else: ?> 
                                                <button class="btn btn-sm btn-outline--dark" disabled>
                                                    <i class="las la-user-check"></i><?php echo app('translator')->get('KYC Data'); ?>
                                                </button>
                                            <?php endif ?>
                                        <?php endif; ?>
                                    </div>
                                </td>

                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage)); ?></td>
                                </tr>
                            <?php endif; ?>

                            </tbody>
                        </table><!-- table end -->
                    </div>
                </div>
                <?php if($users->hasPages()): ?>
                <div class="card-footer py-4">
                    <?php echo e(paginateLinks($users)); ?>

                </div>
                <?php endif; ?>
            </div>
        </div>


    </div>
<?php $__env->stopSection(); ?>



<?php $__env->startPush('breadcrumb-plugins'); ?>
<?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.search-form','data' => ['placeholder' => 'Username / Email']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('search-form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['placeholder' => 'Username / Email']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
    <?php $hasPermission = App\Models\Role::hasPermission('admin.users.add.new.form')  ? 1 : 0;
            if($hasPermission == 1): ?>
        <a href="<?php echo e(route('admin.users.add.new.form')); ?>" class="btn btn-outline--primary h-45">
            <i class="las la-plus"></i><?php echo app('translator')->get('Add New'); ?>
        </a>
    <?php endif ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/users/list.blade.php ENDPATH**/ ?>