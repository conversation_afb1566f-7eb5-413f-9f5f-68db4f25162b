

<?php $__env->startSection('panel'); ?>
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10 bg--transparent shadow-none">
            <div class="card-body p-0">
                <div class="table-responsive--md  table-responsive">
                    <table class="table table--light style--two bg-white">
                        <thead>
                        <tr>
                            
                            <th><?php echo app('translator')->get('Name'); ?></th>
                            <th><?php echo app('translator')->get('Group'); ?></th>
                            <th><?php echo app('translator')->get('URL'); ?></th>
                            <th><?php echo app('translator')->get('Username'); ?></th>
                            <th><?php echo app('translator')->get('Status'); ?></th>
                            <th><?php echo app('translator')->get('Action'); ?></th>
                        </tr> 
                        </thead>  
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $servers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $server): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>  
                                    <td>
                                        <span class="fw-bold"><?php echo e(__($server->name)); ?></span>
                                    </td>
                                    <td>
                                        <?php echo e(__(@$server->group->name)); ?>

                                    </td>
                                    <td>
                                        <?php echo e($server->hostname); ?>

                                    </td>

                                    <td>
                                        <?php echo e(__($server->username)); ?>

                                    </td>

                                    <td>
                                        <?php echo $server->showStatus; ?>
                                    </td>

                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline--primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="las la-ellipsis-v"></i><?php echo app('translator')->get('Action'); ?>
                                        </button>
                                        <div class="dropdown-menu">
                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.server.login.whm')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <a href="<?php echo e(route('admin.server.login.whm', $server->id)); ?>" class="dropdown-item" 
                                                    data-modal_title="<?php echo app('translator')->get('Login to WHM'); ?>"
                                                >
                                                    <i class="lab la-whmcs"></i> <?php echo app('translator')->get('Login to WHM'); ?>
                                                </a>
                                            <?php endif ?>
                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.server.edit.page')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <a href="<?php echo e(route('admin.server.edit.page', $server->id)); ?>" class="dropdown-item"
                                                    data-modal_title="<?php echo app('translator')->get('Edit'); ?>"
                                                >
                                                    <i class="la la-pencil"></i> <?php echo app('translator')->get('Edit'); ?>
                                                </a>
                                            <?php endif ?>
                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.server.status')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <?php if($server->status == 0): ?>
                                                    <a href="javascript:void(0)"
                                                            class="dropdown-item confirmationBtn"
                                                            data-action="<?php echo e(route('admin.server.status', $server->id)); ?>"
                                                            data-question="<?php echo app('translator')->get('Are you sure to enable this server?'); ?>">
                                                        <i class="la la-eye"></i> <?php echo app('translator')->get('Enable'); ?>
                                                    </a>
                                                <?php else: ?>
                                                    <a href="javascript:void(0)" class="dropdown-item confirmationBtn"
                                                    data-action="<?php echo e(route('admin.server.status', $server->id)); ?>"
                                                    data-question="<?php echo app('translator')->get('Are you sure to disable this server?'); ?>">
                                                            <i class="la la-eye-slash"></i> <?php echo app('translator')->get('Disable'); ?>
                                                    </a>
                                                <?php endif; ?>
                                            <?php else: ?> 
                                                <?php if($server->status == 0): ?>
                                                    <a href="javascript:void(0)" class="dropdown-item">
                                                        <i class="la la-eye"></i> <?php echo app('translator')->get('Enable'); ?>
                                                    </a>
                                                <?php else: ?>
                                                    <a href="javascript:void(0)" class="dropdown-item">
                                                        <i class="la la-eye-slash"></i> <?php echo app('translator')->get('Disable'); ?>
                                                    </a>
                                                <?php endif; ?>
                                            <?php endif ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage)); ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table><!-- table end -->
                </div>
            </div>
            <?php if($servers->hasPages()): ?>
                <div class="card-footer py-4">
                    <?php echo e(paginateLinks($servers)); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if (isset($component)) { $__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b = $component; } ?>
<?php $component = App\View\Components\ConfirmationModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('confirmation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\ConfirmationModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b)): ?>
<?php $component = $__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b; ?>
<?php unset($__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b); ?>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('breadcrumb-plugins'); ?>
<div class="justify-content-end d-flex flex-wrap gap-2">
    <?php $hasPermission = App\Models\Role::hasPermission('admin.groups.server')  ? 1 : 0;
            if($hasPermission == 1): ?>
        <a class="btn btn-sm btn-outline--success" href="<?php echo e(route('admin.groups.server')); ?>">
            <i class="las la-plus"></i><?php echo app('translator')->get('Add Server Group'); ?>
        </a>
    <?php endif ?>
    <?php $hasPermission = App\Models\Role::hasPermission('admin.server.add.page')  ? 1 : 0;
            if($hasPermission == 1): ?>
        <a class="btn btn-sm btn-outline--primary" href="<?php echo e(route('admin.server.add.page')); ?>">
            <i class="las la-plus"></i><?php echo app('translator')->get('Add Server'); ?>
        </a>
    <?php endif ?>
</div>
<a href="<?php echo e(session()->get('url') ?? '#'); ?>" class="whmLogin" target="_blank"></a>
<?php $__env->stopPush(); ?> 

<?php $__env->startPush('style'); ?>
<style>
.table-responsive {
    background: transparent;
    min-height: 350px;
}
.dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        (function ($) {
            "use strict";

            var whmLoginUrl = <?php echo json_encode(session()->get('url'), 15, 512) ?>;

            if(whmLoginUrl){
                document.querySelector('.whmLogin').click();
            }

        })(jQuery);
    </script>
<?php $__env->stopPush(); ?> 

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/server/all.blade.php ENDPATH**/ ?>