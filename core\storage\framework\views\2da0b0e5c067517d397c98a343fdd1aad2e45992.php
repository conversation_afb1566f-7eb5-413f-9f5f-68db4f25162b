<?php $__env->startSection('app'); ?>    

    <?php
        $user = auth()->user();
    ?> 

    <?php echo $__env->make($activeTemplate.'partials.auth_header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make($activeTemplate.'partials.breadcrumb', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
 
    <div class="col-12 service-category bg--light">
        <div class="container px-3">
            <div class="row gy-4 justify-content-center">
 
                <div class="row py-3 px-xl-0">

                    <div class="col-lg-3 mb-4"> 
                        <div class="collapable-sidebar">
                            <div class="collapable-sidebar__inner">
                                <button type="button" class="collapable-sidebar__close d-lg-none d-block"><i class="las la-times"></i> </button>
                    
                                <div class="card mb-4">
                                    <div class="card-header border-bottom p-2 bg-dark-two text-center fw-bold"><?php echo app('translator')->get('My Information'); ?></div>
                                    <div class="card-body">
                                        <strong><?php echo e(__($user->fullname)); ?></strong> 
                                        <span class="d-block mt-1"> 
                                            <?php echo e(__(@$user->address->address)); ?>

                                        </span>
                                        <span class="d-block mt-1">
                                            <?php echo e(@$user->address->city ? __(@$user->address->city).',' : null); ?> 
                                            <?php echo e(@$user->address->state ? __(@$user->address->state).',' :  null); ?>

                                            <?php echo e(@$user->address->zip ? __(@$user->address->zip).',' : null); ?>

                                        </span>
                                        <span class="mt-1"><?php echo e(__(@$user->address->country)); ?></span>
                                    </div>
                                    <div class="card-footer">
                                        <a href="<?php echo e(route('user.profile.setting')); ?>" class="btn btn--base border w-100"><i class="fas fa-pencil-alt me-1"></i> <?php echo app('translator')->get('Update'); ?></a>
                                    </div>
                                </div>
                             
                                <div class="card mb-4">
                                    <div class="card-header border-bottom p-2 bg-dark-two text-center fw-bold"><?php echo app('translator')->get('Support'); ?></div> 
                                    <div class="card-body">
                                        <a href="<?php echo e(route('ticket.open')); ?>" class="btn border w-100"><i class="fas fa-plus me-1"></i> <?php echo app('translator')->get('New Ticket'); ?></a>
                                    </div>
                                </div>
                    
                                <nav id="actionMenu" class="collapse d-block sidebar collapse bg-white border">
                                    <div class="position-sticky">
                                        <div class="list-group list-group-flush">
                                            <span class="border-bottom p-2 bg-dark-two text-center fw-bold">
                                                <span><?php echo app('translator')->get('Shortcuts'); ?></span>
                                            </span>  
                                            <?php if(@$serviceCategories->first()): ?>
                                                <a href="<?php echo e(route('service.category', [@$serviceCategories->first()->slug, 'all='])); ?>" class="list-group-item list-group-item-action py-2 ripple">
                                                    <span><?php echo app('translator')->get('Order New Service'); ?></span> 
                                                </a>
                                            <?php endif; ?>
                                            <a href="<?php echo e(route('register.domain')); ?>" class="list-group-item list-group-item-action py-2 ripple">
                                                <span><?php echo app('translator')->get('Register New Domain'); ?></span>
                                            </a>
                                            <a href="<?php echo e(route('user.logout')); ?>" class="list-group-item list-group-item-action py-2 ripple">
                                                <span><?php echo app('translator')->get('Logout'); ?></span> 
                                            </a>
                                        </div>
                                    </div>
                                </nav>
                            </div>
                        </div>
                    </div> 

                    <div class="row d-lg-none d-block">
                        <div class="col-12 ">
                            <div class="show-sidebar-bar dashboard-bar">
                                <i class="las la-bars"></i>
                            </div>
                        </div>
                    </div>

                    <?php echo $__env->yieldContent('content'); ?>
                 </div>

            </div> 
        </div>   
    </div> 

    <?php echo $__env->make($activeTemplate.'partials.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?> 
 



<?php echo $__env->make($activeTemplate.'layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/layouts/master_side_bar.blade.php ENDPATH**/ ?>