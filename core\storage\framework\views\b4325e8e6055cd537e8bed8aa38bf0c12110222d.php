

<?php $__env->startSection('panel'); ?>
<form class="form-horizontal" method="post" action="<?php echo e(route('admin.server.add')); ?>">
<?php echo csrf_field(); ?> 
<div class="row">
    <div class="col-lg-6 form-group">
        <div class="card">
            <div class="card-header w-100 bg--dark">
                <h5 class="text--white"><?php echo app('translator')->get('Name and Hostname'); ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Select Group'); ?></label>
                            <select name="server_group_id" class="form-control" required>
                                <option value="" hidden><?php echo app('translator')->get('Select One'); ?></option>
                                <?php $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($group->id); ?>" ><?php echo e(__($group->name)); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select> 
                        </div> 
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Name'); ?></label>
                            <input type="text" class="form-control" name="name" required value="<?php echo e(old('name')); ?>">
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-2 col-lg-12 col-xxl-3">
                        <div class="form-group">
                            <div class="justify-content-between d-flex flex-wrap">
                                <div>
                                    <label><?php echo app('translator')->get('Protocol'); ?></label>
                                </div>
                            </div>
                            <select name="protocol" class="form-control">
                                <option value="https://" <?php echo e(old('protocol') == 'https://' ? 'selected' : null); ?>><?php echo app('translator')->get('https'); ?></option>
                                <option value="http://" <?php echo e(old('protocol') == 'http://' ? 'selected' : null); ?>><?php echo app('translator')->get('http'); ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-8 col-lg-8 col-xxl-6">
                        <div class="form-group">
                            <div class="justify-content-between d-flex flex-wrap">
                                <div>
                                    <label><?php echo app('translator')->get('Hostname'); ?></label>
                                </div>
                            </div>
                            <input type="text" class="form-control" name="host" required value="<?php echo e(old('host')); ?>">
                        </div>
                    </div>
                    <div class="col-md-2 col-lg-4 col-xxl-3"> 
                        <div class="form-group">
                            <div class="justify-content-between d-flex flex-wrap">
                                <div>
                                    <label><?php echo app('translator')->get('Port'); ?></label>
                                </div>
                            </div>
                            <input type="text" class="form-control" name="port" required value="<?php echo e(old('port')); ?>">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-3">
            <div class="card">
                <div class="card-header bg--dark">
                    <h5 class="text--white"><?php echo app('translator')->get('Server Details'); ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label><?php echo app('translator')->get('Username'); ?></label>
                                <input type="text" class="form-control" name="username" required value="<?php echo e(old('username')); ?>">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label><?php echo app('translator')->get('Password'); ?></label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label><?php echo app('translator')->get('API Token'); ?></label>
                                <input type="text" class="form-control" name="api_token" value="<?php echo e(old('api_token')); ?>" required disabled>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label><?php echo app('translator')->get('Security Token'); ?></label>
                                <input type="text" class="form-control" name="security_token" value="<?php echo e(old('security_token')); ?>" required disabled>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="form-group">
                                <div class="justify-content-between d-flex">
                                    <label><?php echo app('translator')->get('Test'); ?></label>
                                    <div class="connection d-none">
                                        <i>
                                            <i class="icon"></i>
                                            <small class="response"><?php echo app('translator')->get('Attempting to connect to server'); ?>...</small>
                                        </i>
                                     </div>
                                </div>
                                <button type="button" class="btn btn--success h-45 w-100 testConnection"><?php echo app('translator')->get('Test Connection'); ?> <i class="las la-angle-double-right"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6 form-group">
        <div class="card h-100">
            <div class="card-header bg--dark">
                <h5 class="text--white"><?php echo app('translator')->get('Nameservers'); ?></h5>
            </div>
            <div class="card-body">
                <div class="row">
                    
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Primary Nameserver'); ?></label>
                            <input type="text" class="form-control" name="ns1" value="<?php echo e(old('ns1')); ?>" required disabled>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('IP Address'); ?></label>
                            <input type="text" class="form-control" name="ns1_ip" value="<?php echo e(old('ns1_ip')); ?>" required disabled>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Secondary Nameserver'); ?></label>
                            <input type="text" class="form-control" name="ns2" value="<?php echo e(old('ns2')); ?>" required disabled> 
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('IP Address'); ?></label>
                            <input type="text" class="form-control" name="ns2_ip" value="<?php echo e(old('ns2_ip')); ?>" required disabled>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Third Nameserver'); ?></label>
                            <input type="text" class="form-control" name="ns3" value="<?php echo e(old('ns3')); ?>" disabled>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('IP Address'); ?></label>
                            <input type="text" class="form-control" name="ns3_ip" value="<?php echo e(old('ns3_ip')); ?>" disabled>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Fourth Nameserver'); ?></label>
                            <input type="text" class="form-control" name="ns4" value="<?php echo e(old('ns4')); ?>" disabled>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('IP Address'); ?></label>
                            <input type="text" class="form-control" name="ns4_ip" value="<?php echo e(old('ns4_ip')); ?>" disabled>
                        </div>
                    </div>

                </div> 
            </div>
        </div>
    </div>
    <?php $hasPermission = App\Models\Role::hasPermission('admin.server.add')  ? 1 : 0;
            if($hasPermission == 1): ?>
        <div class="col-lg-12 mt-3">
            <button type="submit" class="btn btn--primary h-45 w-100" disabled><?php echo app('translator')->get('Submit'); ?></button>
        </div>
    <?php endif ?>
</div>
</form> 
<?php $__env->stopSection(); ?>

<?php $hasPermission = App\Models\Role::hasPermission('admin.servers')  ? 1 : 0;
            if($hasPermission == 1): ?>
    <?php $__env->startPush('breadcrumb-plugins'); ?>
        <a href="<?php echo e(route('admin.servers')); ?>" class="btn btn-sm btn-outline--primary">
            <i class="la la-undo"></i> <?php echo app('translator')->get('Go to Servers'); ?>
        </a>
    <?php $__env->stopPush(); ?>
<?php endif ?>

<?php $__env->startPush('script'); ?>
    <script>
        (function($){
            "use strict"; 

            var oldGroup = '<?php echo e(old("server_group_id")); ?>'; 
          
            if(oldGroup){
                $('select[name=server_group_id]').val(oldGroup);
            }

            $('.testConnection').on('click', function(){
 
                var hostname = $('input[name=host]').val();
                var protocol = $('select[name=protocol]').val();
                var port = $('input[name=port]').val();
                var username = $('input[name=username]').val();
                var password = $('input[name=password]').val();

                hostname = protocol+hostname+':'+port;

                <?php $hasPermission = App\Models\Role::hasPermission('admin.server.test.connection')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    $.ajax({ 
                        type:'POST',
                        url:'<?php echo e(route("admin.server.test.connection")); ?>',
                        data: {
                            'hostname': hostname,
                            'username': username,
                            'password': password,
                            '_token': '<?php echo e(csrf_token()); ?>',
                        },

                        beforeSend: function() {
                            $('.connection').removeClass('d-none');
                            $('.connection .icon').html('<i class="fas fa-spinner fa-spin"></i>');
                            $('.connection .respone').text('Attempting to connect to server...');
                        },

                        success:function(response){
                            setTimeout(function() {
                                if(response.success){
                                    $('.connection').addClass('d-none');
                                    $("*[disabled]").not(true).removeAttr("disabled");
                                }
                                else if(response.error){
                                    $.each(response.error, function(key, value) {
                                        notify('error', value);
                                    });
                                    $('.connection').addClass('d-none');
                                }
                                else{
                                    notify('error', response.message);
                                    $('.connection .icon').html('<i class="fas fa-times"></i>');
                                }
                            }, 200);
                        }
                    });
                <?php endif ?>
            });

        })(jQuery);    
    </script> 
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/server/add.blade.php ENDPATH**/ ?>