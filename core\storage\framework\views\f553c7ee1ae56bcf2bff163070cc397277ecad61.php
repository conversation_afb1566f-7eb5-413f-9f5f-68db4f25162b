

<?php $__env->startSection('panel'); ?>
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10 bg--transparent shadow-none">
            <div class="card-body p-0">
                <div class="table-responsive--md  table-responsive">
                    <table class="table table--light style--two bg-white">
                        <thead> 
                        <tr>
                            <th><?php echo app('translator')->get('Group Name'); ?></th>
                            <th><?php echo app('translator')->get('Configurable Options'); ?></th>
                            <th><?php echo app('translator')->get('Status'); ?></th>
                            <th><?php echo app('translator')->get('Action'); ?></th>
                        </tr> 
                        </thead>  
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>  
                                <tr>
                                    <td>
                                        <span class="fw-bold"><?php echo e(__($group->name)); ?></span>
                                    </td>
                                
                                    <td>
                                        <?php echo e(@$group->options->count()); ?>

                                    </td>

                                    <td>
                                        <?php echo $group->showStatus; ?>
                                    </td>

                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline--primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="las la-ellipsis-v"></i><?php echo app('translator')->get('Action'); ?>
                                        </button>
                                        <div class="dropdown-menu">
                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.configurable.group.options')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <a href="<?php echo e(route('admin.configurable.group.options', $group->id)); ?>" class="dropdown-item" 
                                                    data-modal_title="<?php echo app('translator')->get('Configurable Options'); ?>"
                                                >
                                                    <i class="la la-clipboard"></i> <?php echo app('translator')->get('Configurable Options'); ?>
                                                </a>
                                            <?php endif ?>

                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.configurable.group.update')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <a href="javascript:void(0)" class="dropdown-item editBtn" data-data="<?php echo e($group); ?>" 
                                                    data-modal_title="<?php echo app('translator')->get('Edit'); ?>"
                                                >
                                                    <i class="la la-pencil"></i> <?php echo app('translator')->get('Edit'); ?>
                                                </a>
                                            <?php endif ?>

                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.configurable.group.status')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <?php if($group->status == 0): ?>
                                                    <a href="javascript:void(0)" class="dropdown-item confirmationBtn"
                                                        data-action="<?php echo e(route('admin.configurable.group.status', $group->id)); ?>"
                                                        data-question="<?php echo app('translator')->get('Are you sure to enable this configurable group?'); ?>"
                                                        data-modal_title="<?php echo app('translator')->get('Enable'); ?>"
                                                    >
                                                        <i class="la la-eye"></i> <?php echo app('translator')->get('Enable'); ?>
                                                    </a>
                                                <?php else: ?>
                                                    <a href="javascript:void(0)" class="dropdown-item confirmationBtn"
                                                        data-action="<?php echo e(route('admin.configurable.group.status', $group->id)); ?>"
                                                        data-question="<?php echo app('translator')->get('Are you sure to disable this configurable group?'); ?>"
                                                        data-modal_title="<?php echo app('translator')->get('Disable'); ?>"
                                                    >
                                                        <i class="la la-eye-slash"></i> <?php echo app('translator')->get('Disable'); ?>
                                                    </a>
                                                <?php endif; ?>
                                            <?php else: ?> 
                                                <?php if($group->status == 0): ?>
                                                    <a href="javascript:void(0)" class="dropdown-item" data-modal_title="<?php echo app('translator')->get('Enable'); ?>">
                                                        <i class="la la-eye"></i> <?php echo app('translator')->get('Enable'); ?>
                                                    </a>
                                                <?php else: ?>
                                                    <a href="javascript:void(0)" class="dropdown-item" data-modal_title="<?php echo app('translator')->get('Disable'); ?>">
                                                        <i class="la la-eye-slash"></i> <?php echo app('translator')->get('Disable'); ?>
                                                    </a>
                                                <?php endif; ?>
                                            <?php endif ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage)); ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table><!-- table end -->
                </div>
            </div>
            <?php if($groups->hasPages()): ?>
                <div class="card-footer py-4">
                    <?php echo e(paginateLinks($groups)); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div> 

<?php if (isset($component)) { $__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b = $component; } ?>
<?php $component = App\View\Components\ConfirmationModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('confirmation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\ConfirmationModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b)): ?>
<?php $component = $__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b; ?>
<?php unset($__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b); ?>
<?php endif; ?>


<div id="createModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createModalLabel"><?php echo app('translator')->get('New Configurable Group'); ?></h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="las la-times"></i>
                </button>
            </div>
            <form class="form-horizontal" method="post" action="<?php echo e(route('admin.configurable.group.add')); ?>">
                <?php echo csrf_field(); ?> 
                <div class="modal-body"> 
                    <div class="form-group">
                        <label><?php echo app('translator')->get('Name'); ?></label> 
                        <input type="text" class="form-control" name="name" required value="<?php echo e(old('name')); ?>" required>
                    </div>
                    <div class="form-group"> 
                        <label><?php echo app('translator')->get('Assigned Products'); ?></label>
                        <select name="assigned_product[]" class="form-control select-h-custom productsId select2-basic" multiple="multiple">
                             <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                <option value="<?php echo e($product->id); ?>"><?php echo e(__($product->name)); ?> - <?php echo e(__(@$product->serviceCategory->name)); ?></option>
                             <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label><?php echo app('translator')->get('Short Description'); ?></label>
                        <textarea name="description" class="form-control" required rows="4"><?php echo e(old('description')); ?></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn--primary h-45 w-100"><?php echo app('translator')->get('Submit'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>


<div id="editModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createModalLabel"><?php echo app('translator')->get('Update Configurable Group'); ?></h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="las la-times"></i>
                </button>
            </div>
            <form class="form-horizontal" method="post" action="<?php echo e(route('admin.configurable.group.update')); ?>">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="id" required>
                <div class="modal-body"> 
                    <div class="form-group">
                        <label><?php echo app('translator')->get('Name'); ?></label>
                        <input type="text" class="form-control" name="name" required required>
                    </div>
                    <div class="form-group">
                        <label><?php echo app('translator')->get('Assigned Products'); ?></label>
                        <select name="assigned_product[]" class="form-control select-h-custom productsId select2-basic" multiple="multiple">
                             <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($product->id); ?>"><?php echo e(__($product->name)); ?> - <?php echo e(__(@$product->serviceCategory->name)); ?></option>
                             <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label><?php echo app('translator')->get('Short Description'); ?></label>
                        <textarea name="description" class="form-control" required rows="4"></textarea>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="submit" class="btn btn--primary h-45 w-100"><?php echo app('translator')->get('Submit'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $hasPermission = App\Models\Role::hasPermission('admin.configurable.group.add')  ? 1 : 0;
            if($hasPermission == 1): ?>
    <?php $__env->startPush('breadcrumb-plugins'); ?>
        <button class="btn btn-sm btn-outline--primary addBtn">
            <i class="las la-plus"></i><?php echo app('translator')->get('Add New'); ?>
        </button>
    <?php $__env->stopPush(); ?>
<?php endif ?>

<?php $__env->startPush('script'); ?>
    <script>
        (function($){
            "use strict";

            $('.addBtn').on('click', function () {
                var modal = $('#createModal');
                modal.modal('show');
            });

            $('.editBtn').on('click', function () {
                var modal = $('#editModal');
                var record = $(this).data('data');
                
                if(record.get_products){
                    var productsId = []; 
                    for(var i = 0; i < record.get_products.length; i++){
                        productsId[i] = record.get_products[i].product_id; 
                    }
        
                    modal.find('.productsId').val(productsId).select2();
                } 
               
                modal.find('input[name=id]').val(record.id);
                modal.find('input[name=name]').val(record.name);
                modal.find('textarea[name=description]').val(record.description);
                modal.find('select[name=service_category_id]').val(record.service_category_id);

                modal.modal('show');
            });

        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('style'); ?>
    <style>
        .select-h-custom{
            height: 110px !important;
        }
        .table-responsive {
            background: transparent;
            min-height: 350px;
        }
        .dropdown-toggle::after {
            display: inline-block;
            margin-left: 0.255em;
            vertical-align: 0.255em;
            content: "";
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/configurable/group.blade.php ENDPATH**/ ?>