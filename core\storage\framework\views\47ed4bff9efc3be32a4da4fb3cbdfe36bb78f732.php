<?php $__env->startSection('content'); ?> 
<div class="pt-60 pb-60 bg--light section-full">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card custom--card style-two"> 
                    <div class="card-header">
                        <h6 class="card-title text-center"><?php echo e(__($pageTitle)); ?></h6>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="<?php echo e(route('user.data.submit')); ?>">
                            <?php echo csrf_field(); ?>
                            <div class="row gy-4"> 
                                <div class="col-sm-6"> 
                                    <div class="form-group">
                                        <label><?php echo app('translator')->get('First Name'); ?> <span class="text--danger">*</span></label>
                                        <input type="text" class="form-control form--control h-45" name="firstname" value="<?php echo e(old('firstname')); ?>" required>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label><?php echo app('translator')->get('Last Name'); ?> <span class="text--danger">*</span></label>
                                        <input type="text" class="form-control form--control h-45" name="lastname" value="<?php echo e(old('lastname')); ?>" required>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label><?php echo app('translator')->get('Address'); ?></label>
                                        <input type="text" class="form-control form--control h-45" name="address" value="<?php echo e(old('address')); ?>">
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label><?php echo app('translator')->get('State'); ?></label>
                                        <input type="text" class="form-control form--control h-45" name="state" value="<?php echo e(old('state')); ?>">
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label><?php echo app('translator')->get('Zip Code'); ?></label>
                                        <input type="text" class="form-control form--control h-45" name="zip" value="<?php echo e(old('zip')); ?>">
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label><?php echo app('translator')->get('City'); ?></label>
                                        <input type="text" class="form-control form--control h-45" name="city" value="<?php echo e(old('city')); ?>">
                                    </div>
                                </div>
                                <div class="col-12 text-end">
                                    <button type="submit" class="btn btn--base w-100"><?php echo app('translator')->get('Submit'); ?></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate.'layouts.frontend', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/user/profile/user_data.blade.php ENDPATH**/ ?>