<form action="" class="form">
    <div class="form-group position-relative mb-0">
        <div class="domain-search-icon"><i class="fas fa-search"></i></div>
        <input class="form-control form--control" type="text" name="domain" required placeholder="<?php echo app('translator')->get('Domain name or keyword'); ?>" value="<?php echo e(@request()->domain); ?>">
        <div class="domain-search-icon-reset">
            <button class="btn btn--base" type="submit"><?php echo app('translator')->get('Search'); ?></button>
        </div>
    </div>
</form>

<?php $__env->startPush('script'); ?>
    <script>
        (function($) {
            "use strict";
            $('.form').on('submit', function(e) {
                e.preventDefault();
                var domain = $(this).find('input[name=domain]').val();
                window.location.href = "<?php echo e(route('register.domain')); ?>?domain=" + domain;
            })
        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/partials/domain_search_form.blade.php ENDPATH**/ ?>