<ul class="nav nav-tabs mb-4 topTap breadcum-nav" role="tablist">
    <button class="breadcum-nav-close"><i class="las la-times"></i></button>
    <li class="nav-item" role="presentation">
      <?php $hasPermission = App\Models\Role::hasPermission('admin.gateway.automatic.index')  ? 1 : 0;
            if($hasPermission == 1): ?>
        <a href="<?php echo e(route('admin.gateway.automatic.index')); ?>" class="nav-link text-dark" type="button">
      <?php else: ?> 
        <a href="javascript:void(0)" class="nav-link text-dark disabled" type="button">
      <?php endif ?>
        <i class="las la-money-bill-wave"></i> <?php echo app('translator')->get('Automatic Gateways'); ?>
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <?php $hasPermission = App\Models\Role::hasPermission('admin.gateway.manual.index')  ? 1 : 0;
            if($hasPermission == 1): ?>
            <a href="<?php echo e(route('admin.gateway.manual.index')); ?>" class="nav-link text-dark" type="button">
        <?php else: ?> 
            <a href="javascript:void(0)" class="nav-link text-dark disabled" type="button">
        <?php endif ?>
            <i class="las la-hand-holding-usd"></i> <?php echo app('translator')->get('Manual Gateways'); ?>
        </a>
    </li>
</ul>

<?php $__env->startPush('script'); ?>
<script> 
    (function($) {
        "use strict";
       
        $(`.topTap li a[href='<?php echo e(url()->current()); ?>']`).addClass('active text--primary');
        $(`.topTap li a[href='<?php echo e(url()->current()); ?>']`).closest('li').addClass('active text--primary');

        $('.breadcum-nav-open').on('click', function(){
            $(this).toggleClass('active');
            $('.breadcum-nav').toggleClass('active');
        });

        $('.breadcum-nav-close').on('click', function(){
            $('.breadcum-nav').removeClass('active');
        });

    })(jQuery);
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('style'); ?>
<style>
    .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active{
        background-color: #f3f3f9;
        border-color: #dee2e6 #dee2e6 #f3f3f9;
    }
</style>
<?php $__env->stopPush(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/gateways/top_tap.blade.php ENDPATH**/ ?>