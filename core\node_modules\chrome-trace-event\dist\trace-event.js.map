{"version": 3, "file": "trace-event.js", "sourceRoot": "", "sources": ["../lib/trace-event.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAEH,iCAAqE;AAYrE;IACE,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,yBAAyB;IACxD,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,eAAe;IAC5E,OAAO;QACL,EAAE,IAAA;QACF,GAAG,EAAE,OAAO,CAAC,GAAG;QAChB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,gCAAgC;KAClD,CAAC;AACJ,CAAC;AAiBD;IAA4B,kCAAc;IAUxC,gBAAY,IAAwB;QAAxB,qBAAA,EAAA,SAAwB;QAApC,YACE,iBAAO,SA6DR;QAnEO,cAAQ,GAAY,KAAK,CAAC;QAC1B,YAAM,GAAY,EAAE,CAAC;QAM3B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;SAC/D;QAED,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;YAC1D,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;SACvE;QAED,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;YAC1D,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;SACvE;QAED,IACE,IAAI,CAAC,UAAU,IAAI,IAAI;YACvB,CAAC,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,EACvD;YACA,MAAM,IAAI,KAAK,CACb,yDAAyD,CAC1D,CAAC;SACH;QAED,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC;QACvC,KAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAE1B,IAAI,KAAI,CAAC,MAAM,EAAE;YACf,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SACpE;aAAM;YACL,KAAI,CAAC,MAAM,GAAG,EAAE,CAAC;SAClB;QACD,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,MAAM,CAAC,MAAM,CAAC,KAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;SACzC;QACD,IAAI,CAAC,KAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YACpB,2DAA2D;YAC3D,KAAI,CAAC,MAAM,CAAC,GAAG,GAAG,SAAS,CAAC;SAC7B;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YACzC,KAAI,CAAC,MAAM,CAAC,GAAG,GAAG,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAC7C;QACD,IAAI,CAAC,KAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YACrB,4DAA4D;YAC5D,KAAI,CAAC,MAAM,CAAC,IAAI,GAAG,EAAE,CAAC;SACvB;QAED,IAAI,KAAI,CAAC,MAAM,EAAE;YACf,+DAA+D;YAC/D,kDAAkD;YAClD,8DAA8D;YAC9D,KAAI,CAAC,KAAK,GAAG,KAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC;SAClD;aAAM;YACL,KAAI,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC5C,IAAI,UAAU,GAAoB,EAAE,UAAU,EAAE,KAAI,CAAC,WAAW,EAAE,CAAC;YACnE,IAAI,KAAI,CAAC,WAAW,EAAE;gBACpB,KAAI,CAAC,KAAK,GAAG,KAAI,CAAC,IAAI,CAAC;aACxB;iBAAM;gBACL,KAAI,CAAC,KAAK,GAAG,KAAI,CAAC,WAAW,CAAC;gBAC9B,UAAU,CAAC,QAAQ,GAAG,MAAM,CAAC;aAC9B;YAED,iBAAc,CAAC,IAAI,CAAC,KAAI,EAAE,UAAU,CAAC,CAAC;SACvC;;IACH,CAAC;IAED;;;OAGG;IACI,sBAAK,GAAZ;QACE,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YAC1B,KAAkB,UAAW,EAAX,KAAA,IAAI,CAAC,MAAM,EAAX,cAAW,EAAX,IAAW;gBAAxB,IAAM,GAAG,SAAA;gBACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACjB;YACD,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;IACH,CAAC;IAED,sBAAK,GAAL,UAAM,CAAS,IAAG,CAAC;IAEX,4BAAW,GAAnB,UAAoB,EAAS;QAC3B,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACf,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACvB;aAAM;YACL,SAAS,GAAG,KAAK,CAAC;SACnB;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IACpD,CAAC;IAEO,uBAAM,GAAd;QACE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAChB;IACH,CAAC;IAEM,sBAAK,GAAZ,UAAa,MAAc;QACzB,OAAO,IAAI,MAAM,CAAC;YAChB,MAAM,EAAE,IAAI;YACZ,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;IACL,CAAC;IAEM,sBAAK,GAAZ,UAAa,MAAc;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;IAEM,oBAAG,GAAV,UAAW,MAAc;QACvB,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;IAEM,8BAAa,GAApB,UAAqB,MAAc;QACjC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;IAEM,6BAAY,GAAnB,UAAoB,MAAc;QAChC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;IACvC,CAAC;IAEM,4BAAW,GAAlB,UAAmB,EAAU;QAA7B,iBA0BC;QAzBC,OAAO,UAAC,MAAc;YACpB,IAAI,EAAE,GAAG,QAAQ,EAAE,CAAC;YACpB,0BAA0B;YAC1B,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;YAEX,IAAI,MAAM,EAAE;gBACV,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;oBAC9B,EAAE,CAAC,IAAI,GAAG,MAAM,CAAC;iBAClB;qBAAM;oBACL,KAAgB,UAAmB,EAAnB,KAAA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAnB,cAAmB,EAAnB,IAAmB;wBAA9B,IAAM,CAAC,SAAA;wBACV,IAAI,CAAC,KAAK,KAAK,EAAE;4BACf,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yBAC/B;6BAAM;4BACL,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;yBACnB;qBACF;iBACF;aACF;YAED,IAAI,CAAC,KAAI,CAAC,QAAQ,EAAE;gBAClB,KAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;aAChB;iBAAM;gBACL,KAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACtB;QACH,CAAC,CAAC;IACJ,CAAC;IACH,aAAC;AAAD,CAAC,AA5JD,CAA4B,iBAAc,GA4JzC;AA5JY,wBAAM;AA8JnB;;;;;;;;;;;;;;GAcG"}