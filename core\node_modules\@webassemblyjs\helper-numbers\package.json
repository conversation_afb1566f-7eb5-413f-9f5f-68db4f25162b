{"name": "@webassemblyjs/helper-numbers", "version": "1.11.1", "description": "Number parsing utility", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "dependencies": {"@webassemblyjs/floating-point-hex-parser": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@xtuc/long": "4.2.2"}, "author": "<PERSON>", "license": "MIT", "gitHead": "3f07e2db2031afe0ce686630418c542938c1674b"}