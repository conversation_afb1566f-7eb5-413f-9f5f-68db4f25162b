

<?php $__env->startSection('content'); ?>
<div class="col-12 service-category bg--light section-full">
    <div class="container px-3">  
        <div class="row gy-2"> 
        
            <div class="col-lg-3">
                <div class="collapable-sidebar">
                    <div class="collapable-sidebar__inner">
                        <button type="button" class="collapable-sidebar__close d-lg-none d-block"><i class="las la-times"></i> </button>
                        <nav id="categoryMenu" class="collapse d-block sidebar collapse bg-white border">
                            <span class="border-bottom p-2 bg-dark-two text-center fw-bold w-100"><?php echo app('translator')->get('Service Categories'); ?></span>
                            <div class="position-sticky">
                                <div class="list-group list-group-flush">
                                    <?php $__currentLoopData = $serviceCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <a href="<?php echo e(route('service.category', $category->slug)); ?>" class="list-group-item list-group-item-action py-2 ripple" data-slug="<?php echo e($category->slug); ?>">
                                            <span><?php echo e(__($category->name)); ?></span>
                                        </a>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </nav>
                        <nav id="actionMenu" class="collapse d-block sidebar collapse bg-white border mt-4">
                            <span class="border-bottom p-2 bg-dark-two text-center fw-bold w-100"><?php echo app('translator')->get('Actions'); ?></span>
                            <div class="position-sticky">
                                <div class="list-group list-group-flush">
                                    <a href="<?php echo e(route('register.domain')); ?>" class="list-group-item list-group-item-action py-2 ripple">
                                        <span><?php echo app('translator')->get('Register New Domain'); ?></span>
                                    </a>
                                    <a href="<?php echo e(route('shopping.cart')); ?>" class="list-group-item list-group-item-action py-2 ripple">
                                        <span><?php echo app('translator')->get('View Cart'); ?></span>
                                    </a>
                                </div>
                            </div>
                        </nav>
                    </div>
                </div>
            </div>

            <div class="row d-lg-none d-block">
                <div class="col-12 ">
                    <div class="show-sidebar-bar">
                        <i class="las la-bars"></i>
                    </div>
                </div>
            </div> 

            <?php echo $__env->yieldContent('data'); ?>

        </div>
    </div> 
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        'use strict';
        (function($) {

            $('#categoryMenu a[data-slug="<?php echo e(@$serviceCategory->slug); ?>"]').addClass('bg--base text-white');
            $('#actionMenu a[href="<?php echo e(url()->current()); ?>"]').addClass('bg--base text-white');

        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make($activeTemplate . 'layouts.frontend', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/layouts/side_bar.blade.php ENDPATH**/ ?>