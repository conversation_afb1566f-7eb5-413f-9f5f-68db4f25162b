{"name": "postcss-normalize-unicode", "version": "5.0.4", "description": "Normalize unicode-range descriptors, and can convert to wildcard ranges.", "main": "src/index.js", "files": ["src", "LICENSE-MIT"], "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"browserslist": "^4.16.6", "postcss-value-parser": "^4.2.0"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "devDependencies": {"postcss": "^8.2.15"}, "peerDependencies": {"postcss": "^8.2.15"}, "readme": "# [postcss][postcss]-normalize-unicode\n\n> Normalize unicode with PostCSS.\n\n## Install\n\nWith [npm](https://npmjs.org/package/postcss-normalize-unicode) do:\n\n```\nnpm install postcss-normalize-unicode --save\n```\n\n## Example\n\n### Input\n\n```css\n@font-face{\n    font-family: test;\n    unicode-range: u+2b00-2bff\n}\n```\n\n### Output\n\n```css\n@font-face{\n    font-family: test;\n    unicode-range: u+2b??\n}\n``` \n\n## Usage\n\nSee the [PostCSS documentation](https://github.com/postcss/postcss#usage) for\nexamples for your environment.\n\n## Contributors\n\nSee [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).\n\n## License\n\nMIT © [<PERSON>](http://beneb.info)\n\n[postcss]: https://github.com/postcss/postcss\n"}