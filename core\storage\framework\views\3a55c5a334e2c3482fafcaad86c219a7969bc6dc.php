<?php
    $policyPages = getContent('policy_pages.element', orderById: true);
?>

<?php $__env->startSection('auth'); ?>
    <form action="<?php echo e(route('user.register')); ?>" class="account-form verify-gcaptcha" method="POST">
        <?php echo csrf_field(); ?>
        <div class="mb-4">
            <h4 class="mb-2"><?php echo app('translator')->get('Create an Account'); ?></h4>
            <p><?php echo app('translator')->get('You can create account using email or username and the registration is fully free'); ?></p>
        </div>
        <div class="row gy-3">
            <?php if(session()->get('reference') != null): ?>
                <div class="col-12">
                    <div class="form-group">
                        <label><?php echo app('translator')->get('Reference by'); ?> <span class="text--danger">*</span></label>
                        <input type="text" name="referBy" class="form-control form--control h-45" value="<?php echo e(session()->get('reference')); ?>" readonly>
                    </div>
                </div>
            <?php endif; ?>
            <div class="col-12">
                <div class="form-group">
                    <label><?php echo app('translator')->get('Username'); ?> <span class="text--danger">*</span></label>
                    <input type="text" name="username" value="<?php echo e(old('username')); ?>" class="form-control form--control h-45 checkUser" required>
                    <small class="text--danger usernameExist"></small>
                </div>
            </div>
            <div class="col-12">
                <div class="form-group">
                    <label><?php echo app('translator')->get('E-Mail Address'); ?> <span class="text--danger">*</span></label>
                    <input type="email" name="email" value="<?php echo e(old('email')); ?>" class="form-control form--control h-45 checkUser" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label><?php echo app('translator')->get('Country'); ?> <span class="text--danger">*</span></label>
                    <select name="country" class="form-select form--control h-45">
                        <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option data-mobile_code="<?php echo e($country->dial_code); ?>" value="<?php echo e($country->country); ?>" data-code="<?php echo e($key); ?>">
                                <?php echo e(__($country->country)); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label><?php echo app('translator')->get('Mobile'); ?> <span class="text--danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text mobile-code"></span>
                        <input type="hidden" name="mobile_code">
                        <input type="hidden" name="country_code">
                        <input type="number" name="mobile" value="<?php echo e(old('mobile')); ?>" class="form-control form--control h-45 checkUser" required>
                    </div>
                    <small class="text--danger mobileExist"></small>
                </div>
            </div>
            <div class="col-12">
                <div class="form-group">
                    <label><?php echo app('translator')->get('Password'); ?> <span class="text--danger">*</span></label>
                    <div class="input-group">
                        <input type="password" class="form-control form--control h-45" name="password" required>
                        <?php if($general->secure_password): ?>
                            <div class="input-popup">
                                <p class="error lower"><?php echo app('translator')->get('1 small letter minimum'); ?></p>
                                <p class="error capital"><?php echo app('translator')->get('1 capital letter minimum'); ?></p>
                                <p class="error number"><?php echo app('translator')->get('1 number minimum'); ?></p>
                                <p class="error special"><?php echo app('translator')->get('1 special character minimum'); ?></p>
                                <p class="error minimum"><?php echo app('translator')->get('6 character password'); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-12">
                <div class="form-group">
                    <label><?php echo app('translator')->get('Confirm Password'); ?> <span class="text--danger">*</span></label>
                    <input type="password" class="form-control form--control h-45" name="password_confirmation" required>
                </div>
            </div>

            <?php if (isset($component)) { $__componentOriginalc0af13564821b3ac3d38dfa77d6cac9157db8243 = $component; } ?>
<?php $component = App\View\Components\Captcha::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('captcha'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Captcha::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc0af13564821b3ac3d38dfa77d6cac9157db8243)): ?>
<?php $component = $__componentOriginalc0af13564821b3ac3d38dfa77d6cac9157db8243; ?>
<?php unset($__componentOriginalc0af13564821b3ac3d38dfa77d6cac9157db8243); ?>
<?php endif; ?>

            <?php if($general->agree): ?>
                <div class="col-12">
                    <div class="d-flex flex-wrap gap-2 justify-content-between">
                        <div class="form-group custom--checkbox">
                            <input type="checkbox" id="agree" <?php if(old('agree')): echo 'checked'; endif; ?> name="agree" class="form-check-input" required>
                            <label for="agree"><?php echo app('translator')->get('I agree with'); ?></label>

                            <?php $__currentLoopData = $policyPages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $policy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <a href="<?php echo e(route('policy.pages', [slug($policy->data_values->title), $policy->id])); ?>" target="_blank">
                                    <?php echo e(__($policy->data_values->title)); ?>

                                </a> <?php echo e(!$loop->last ? ',' : null); ?>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <div class="col-12">
                <button type="submit" class="btn btn--base w-100"><?php echo app('translator')->get('Submit'); ?></button>
            </div>
            <div class="col-12">
                <p class="text-center">
                    <?php echo app('translator')->get('Have an account?'); ?> <a href="<?php echo e(route('user.login')); ?>" class="fw-bold text--base"><?php echo app('translator')->get('Login Here'); ?></a>
                </p>
            </div>
        </div>
    </form>

    <div class="modal fade" id="existModalCenter" tabindex="-1" role="dialog" aria-labelledby="existModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="existModalLongTitle"><?php echo app('translator')->get('You are with us'); ?></h5>
                    <span type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </span>
                </div>
                <div class="modal-body">
                    <span class="text-center d-block"><?php echo app('translator')->get('You already have an account please Login '); ?></span>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-dark btn--sm" data-bs-dismiss="modal"><?php echo app('translator')->get('Close'); ?></button>
                    <a href="<?php echo e(route('user.login')); ?>" class="btn btn--base btn--sm"><?php echo app('translator')->get('Login'); ?></a>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php if($general->secure_password): ?>
    <?php $__env->startPush('script-lib'); ?>
        <script src="<?php echo e(asset('assets/global/js/secure_password.js')); ?>"></script>
    <?php $__env->stopPush(); ?>
<?php endif; ?>

<?php $__env->startPush('script'); ?>
    <script>
        "use strict";
        (function($) {
            <?php if($mobileCode): ?>
                $(`option[data-code=<?php echo e($mobileCode); ?>]`).attr('selected', '');
            <?php endif; ?>

            $('select[name=country]').change(function() {
                $('input[name=mobile_code]').val($('select[name=country] :selected').data('mobile_code'));
                $('input[name=country_code]').val($('select[name=country] :selected').data('code'));
                $('.mobile-code').text('+' + $('select[name=country] :selected').data('mobile_code'));
            });
            $('input[name=mobile_code]').val($('select[name=country] :selected').data('mobile_code'));
            $('input[name=country_code]').val($('select[name=country] :selected').data('code'));
            $('.mobile-code').text('+' + $('select[name=country] :selected').data('mobile_code'));

            $('.checkUser').on('focusout', function(e) {
                var url = '<?php echo e(route('user.checkUser')); ?>';
                var value = $(this).val();
                var token = '<?php echo e(csrf_token()); ?>';
                if ($(this).attr('name') == 'mobile') {
                    var mobile = `${$('.mobile-code').text().substr(1)}${value}`;
                    var data = {
                        mobile: mobile,
                        _token: token
                    }
                }
                if ($(this).attr('name') == 'email') {
                    var data = {
                        email: value,
                        _token: token
                    }
                }
                if ($(this).attr('name') == 'username') {
                    var data = {
                        username: value,
                        _token: token
                    }
                }
                $.post(url, data, function(response) {
                    if (response.data != false && response.type == 'email') {
                        $('#existModalCenter').modal('show');
                    } else if (response.data != false) {
                        $(`.${response.type}Exist`).text(`${response.type} already exist`);
                    } else {
                        $(`.${response.type}Exist`).text('');
                    }
                });
            });
        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/user/auth/register.blade.php ENDPATH**/ ?>