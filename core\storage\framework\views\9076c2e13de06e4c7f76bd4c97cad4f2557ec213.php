

<?php $__env->startSection('panel'); ?>
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10 ">
            <div class="card-body p-0">
                <div class="table-responsive--md  table-responsive">
                    <table class="table table--light style--two">
                        <thead> 
                        <tr>
                            <th><?php echo app('translator')->get('Extension'); ?></th>
                            <th><?php echo app('translator')->get('ID Protection'); ?></th>
                            <th><?php echo app('translator')->get('Status'); ?></th>
                            <th><?php echo app('translator')->get('Action'); ?></th>
                        </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $domains; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $domain): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>  
                                <tr>
                                    <td>
                                        <span class="fw-bold"><?php echo e($domain->extension); ?></span>
                                    </td>
                                    <td>
                                        <?php echo $domain->showIdProtection; ?>
                                    </td>
                                    <td>
                                        <?php echo $domain->showStatus; ?>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline--primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="las la-ellipsis-v"></i><?php echo app('translator')->get('Action'); ?>
                                        </button>
                                        <div class="dropdown-menu">
                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.tld.update.pricing')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <a href="javascript:void(0)" class="dropdown-item priceModal" 
                                                    data-modal_title="<?php echo app('translator')->get('Pricing'); ?>"
                                                    data-pricing="<?php echo e($domain->pricing); ?>" data-id="<?php echo e($domain->pricing->id); ?>" data-ex="<?php echo e($domain->extension); ?>"
                                                >
                                                    <i class="la la-money-bill-wave"></i> <?php echo app('translator')->get('Pricing'); ?>
                                                </a>
                                            <?php endif ?>

                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.tld.update')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <a href="javascript:void(0)" class="dropdown-item editBtn" data-data="<?php echo e($domain); ?>" 
                                                    data-modal_title="<?php echo app('translator')->get('Edit'); ?>"
                                                >
                                                    <i class="la la-pencil"></i> <?php echo app('translator')->get('Edit'); ?>
                                                </a>
                                            <?php endif ?>

                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.tld.status')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <?php if($domain->status == 0): ?>
                                                    <a href="javascript:void(0)"
                                                            class="dropdown-item confirmationBtn"
                                                            data-action="<?php echo e(route('admin.tld.status', $domain->id)); ?>"
                                                            data-question="<?php echo app('translator')->get('Are you sure to enable this TLD?'); ?>">
                                                        <i class="la la-eye"></i> <?php echo app('translator')->get('Enable'); ?>
                                                    </a>
                                                <?php else: ?>
                                                    <a href="javascript:void(0)" class="dropdown-item confirmationBtn"
                                                    data-action="<?php echo e(route('admin.tld.status', $domain->id)); ?>"
                                                    data-question="<?php echo app('translator')->get('Are you sure to disable this TLD?'); ?>">
                                                        <i class="la la-eye-slash"></i> <?php echo app('translator')->get('Disable'); ?>
                                                    </a>
                                                <?php endif; ?>
                                            <?php else: ?> 
                                                <?php if($domain->status == 0): ?>
                                                    <a href="javascript:void(0)" class="dropdown-item">
                                                        <i class="la la-eye"></i> <?php echo app('translator')->get('Enable'); ?>
                                                    </a>
                                                <?php else: ?>
                                                    <a href="javascript:void(0)" class="dropdown-item">
                                                        <i class="la la-eye-slash"></i> <?php echo app('translator')->get('Disable'); ?>
                                                    </a>
                                                <?php endif; ?>
                                            <?php endif ?>
                                        </div>
                                    </td> 
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage)); ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table><!-- table end -->
                </div>
            </div>
            <?php if($domains->hasPages()): ?>
                <div class="card-footer py-4">
                    <div class="card-footer py-4">
                        <?php echo e(paginateLinks($domains)); ?>

                    </div>
                </div> 
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if (isset($component)) { $__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b = $component; } ?>
<?php $component = App\View\Components\ConfirmationModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('confirmation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\ConfirmationModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b)): ?>
<?php $component = $__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b; ?>
<?php unset($__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b); ?>
<?php endif; ?>


<div id="createModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createModalLabel"><?php echo app('translator')->get('Domain Extension/TLD'); ?></h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="las la-times"></i>
                </button>
            </div> 
            <form class="form-horizontal" method="post" action="<?php echo e(route('admin.tld.add')); ?>">
                <?php echo csrf_field(); ?> 
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label><?php echo app('translator')->get('Extension'); ?></label>
                                <input type="text" class="form-control" name="extension" value="<?php echo e(old('extension')); ?>" required autocomplete="off">
                            </div>
                        </div> 
                        <div class="col-md-12">
                            <div class="form-group">
                                <label><?php echo app('translator')->get('ID Protection'); ?></label>
                                <input type="checkbox" data-width="100%" data-size="large" data-onstyle="-success" data-offstyle="-danger" data-bs-toggle="toggle" data-height="50" data-on="<?php echo app('translator')->get('Enable'); ?>" data-off="<?php echo app('translator')->get('Disable'); ?>" name="id_protection" checked>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn--primary h-45 w-100"><?php echo app('translator')->get('Submit'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

  
<div id="editModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createModalLabel"><?php echo app('translator')->get('Update Domain Extension/TLD'); ?></h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="las la-times"></i>
                </button>
            </div>
            <form class="form-horizontal" method="post" action="<?php echo e(route('admin.tld.update')); ?>">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="id" required>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label><?php echo app('translator')->get('Extension'); ?></label>
                                <input type="text" class="form-control" name="extension" required autocomplete="off">
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="form-group">
                                <label><?php echo app('translator')->get('ID Protection'); ?></label>
                                <input type="checkbox" data-width="100%" data-size="large" data-onstyle="-success" data-offstyle="-danger" data-toggle="toggle" data-on="<?php echo app('translator')->get('Enable'); ?>" data-off="<?php echo app('translator')->get('Disable'); ?>" name="id_protection">
                            </div>
                        </div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="submit" class="btn btn--primary h-45 w-100"><?php echo app('translator')->get('Submit'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

  
<div class="modal fade" id="priceModal" tabindex="-1" role="dialog" aria-labelledby="createModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo app('translator')->get('Domain/TLD Pricing'); ?> <span class="domainExtension"></span></h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="las la-times"></i>
                </button>
            </div>
            <form action="<?php echo e(route('admin.tld.update.pricing')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="id" required>
                <div class="modal-body">

                    <div class="row">
                        <div class="col-lg-4 mt-4">
                            <div class="custom-pricing">
                                <div class="border-line-area">
                                    <h6 class="border-line-title text-center fw-bold">
                                        <label for="one_active"><?php echo app('translator')->get('One Year'); ?></label>
                                        <input type="checkbox" name="one_active" id="one_active" class="price_active">
                                    </h6>
                                </div>
                                <div class="row one_active">
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('Price'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="one_year_price" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('ID Protection'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="one_year_id_protection" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('Renewal'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="one_year_renew" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4  mt-4">
                            <div class="custom-pricing">
                                <div class="border-line-area">
                                    <h6 class="border-line-title text-center fw-bold">
                                        <label for="two_active"><?php echo app('translator')->get('Two Year'); ?></label>
                                        <input type="checkbox" name="two_active" id="two_active" class="price_active">
                                    </h6>
                                </div>
                                <div class="row two_active">
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('Price'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="two_year_price" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('ID Protection'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="two_year_id_protection" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('Renewal'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="two_year_renew" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 mt-4">
                            <div class="custom-pricing">
                                <div class="border-line-area">
                                    <h6 class="border-line-title text-center fw-bold">
                                        <label for="three_active"><?php echo app('translator')->get('Three Year'); ?></label>
                                        <input type="checkbox" name="three_active" id="three_active" class="price_active">
                                    </h6>
                                </div>
                                <div class="row three_active">
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('Price'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="three_year_price" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('ID Protection'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="three_year_id_protection" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('Renewal'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="three_year_renew" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-4 mt-4">
                            <div class="custom-pricing">
                                <div class="border-line-area">
                                    <h6 class="border-line-title text-center fw-bold">
                                        <label for="four_active"><?php echo app('translator')->get('Four Year'); ?></label>
                                        <input type="checkbox" name="four_active" id="four_active" class="price_active">
                                    </h6>
                                </div>
                                <div class="row four_active">
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('Price'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="four_year_price" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('ID Protection'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="four_year_id_protection" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('Renewal'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="four_year_renew" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 mt-4">
                            <div class="custom-pricing">
                                <div class="border-line-area">
                                    <h6 class="border-line-title text-center fw-bold">
                                        <label for="five_active"><?php echo app('translator')->get('Five Year'); ?></label>
                                        <input type="checkbox" name="five_active" id="five_active" class="price_active">
                                    </h6>
                                </div>
                                <div class="row five_active">
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('Price'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="five_year_price" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('ID Protection'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="five_year_id_protection" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('Renewal'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="five_year_renew" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 mt-4">
                            <div class="custom-pricing">
                                <div class="border-line-area">
                                    <h6 class="border-line-title text-center fw-bold">
                                        <label for="six_active"><?php echo app('translator')->get('Six Year'); ?></label>
                                        <input type="checkbox" name="six_active" id="six_active" class="price_active">
                                    </h6>
                                </div>
                                <div class="row six_active">
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('Price'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="six_year_price" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('ID Protection'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="six_year_id_protection" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label><?php echo app('translator')->get('Renewal'); ?></label>
                                            <div class="input-group">
                                                <span class="input-group-text"><?php echo e(__($general->cur_sym)); ?></span>
                                                <input type="number" class="form-control" placeholder="0" name="six_year_renew" step="any" required/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn--primary h-45 w-100"><?php echo app('translator')->get('Submit'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $hasPermission = App\Models\Role::hasPermission('admin.tld.add')  ? 1 : 0;
            if($hasPermission == 1): ?>
    <?php $__env->startPush('breadcrumb-plugins'); ?> 
        <button class="btn btn-sm btn-outline--primary addBtn">
            <i class="las la-plus"></i><?php echo app('translator')->get('Add New'); ?>
        </button>
    <?php $__env->stopPush(); ?>
<?php endif ?>

<?php $__env->startPush('style'); ?>
    <style>
        .dropdown-toggle::after {
            display: inline-block;
            margin-left: 0.255em;
            vertical-align: 0.255em;
            content: "";
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        (function($){
            "use strict";  

            $('.addBtn').on('click', function () {
                var modal = $('#createModal'); 
                modal.modal('show');
            });

            $('.editBtn').on('click', function () {
                var modal = $('#editModal');
                var record = $(this).data('data');
                
                modal.find('input[name=id]').val(record.id);
                modal.find('input[name=extension]').val(record.extension);

                if(record.id_protection == 1){
                    modal.find('input[name=id_protection]').bootstrapToggle('on');
                }else{
                    modal.find('input[name=id_protection]').bootstrapToggle('off');
                }

                modal.modal('show');
            });

            $('.priceModal').on('click', function () {
                var modal = $('#priceModal');
                
                modal.find('.domainExtension').text($(this).data('ex'));
                modal.find('form')[0].reset();     
 
                var pricing = $(this).data('pricing');
                modal.find('input[name=id]').val($(this).data('id'));

                modal.find('input[name=one_year_price]').val(parseFloat(pricing.one_year_price).toFixed(2));
                modal.find('input[name=one_year_id_protection]').val(parseFloat(pricing.one_year_id_protection).toFixed(2));
                modal.find('input[name=one_year_renew]').val(parseFloat(pricing.one_year_renew).toFixed(2));

                priceToogle(pricing.one_year_price, 'one_active');

                
                modal.find('input[name=two_year_price]').val(parseFloat(pricing.two_year_price).toFixed(2));
                modal.find('input[name=two_year_id_protection]').val(parseFloat(pricing.two_year_id_protection).toFixed(2));
                modal.find('input[name=two_year_renew]').val(parseFloat(pricing.two_year_renew).toFixed(2));

                priceToogle(pricing.two_year_price, 'two_active');

                modal.find('input[name=three_year_price]').val(parseFloat(pricing.three_year_price).toFixed(2));
                modal.find('input[name=three_year_id_protection]').val(parseFloat(pricing.three_year_id_protection).toFixed(2));
                modal.find('input[name=three_year_renew]').val(parseFloat(pricing.three_year_renew).toFixed(2));

                priceToogle(pricing.three_year_price, 'three_active');

                modal.find('input[name=four_year_price]').val(parseFloat(pricing.four_year_price).toFixed(2));
                modal.find('input[name=four_year_id_protection]').val(parseFloat(pricing.four_year_id_protection).toFixed(2));
                modal.find('input[name=four_year_renew]').val(parseFloat(pricing.four_year_renew).toFixed(2));

                priceToogle(pricing.four_year_price, 'four_active');

                modal.find('input[name=five_year_price]').val(parseFloat(pricing.five_year_price).toFixed(2));
                modal.find('input[name=five_year_id_protection]').val(parseFloat(pricing.five_year_id_protection).toFixed(2));
                modal.find('input[name=five_year_renew]').val(parseFloat(pricing.five_year_renew).toFixed(2));

                priceToogle(pricing.five_year_price, 'five_active');

                modal.find('input[name=six_year_price]').val(parseFloat(pricing.six_year_price).toFixed(2));
                modal.find('input[name=six_year_id_protection]').val(parseFloat(pricing.six_year_id_protection).toFixed(2));
                modal.find('input[name=six_year_renew]').val(parseFloat(pricing.six_year_renew).toFixed(2));

                priceToogle(pricing.six_year_price, 'six_active');

                modal.modal('show');
            });
  
            $('.price_active').on('click', function(){
                var selectorName = $(this).prop('name');

                if($(this).prop('checked') == true){ 
                    $('.'+selectorName).removeClass('d-none');
                    $(`.${selectorName} :input`).first().val(0);
                }else{
                    $('.'+selectorName).addClass('d-none');
                    $(`.${selectorName} :input`).first().val(-1);
                }
            }); 

            function priceToogle(price, selectorName){
                if(price >= 0){
                    $('#'+selectorName).prop('checked', true);
                    $('.'+selectorName).removeClass('d-none');
                }else{
                    $('.'+selectorName).addClass('d-none');
                    $('#'+selectorName).prop('checked', false);
                }
            }

        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

  
<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/tld/all.blade.php ENDPATH**/ ?>