# pretty-time [![NPM version](https://img.shields.io/npm/v/pretty-time.svg?style=flat)](https://www.npmjs.com/package/pretty-time) [![NPM monthly downloads](https://img.shields.io/npm/dm/pretty-time.svg?style=flat)](https://npmjs.org/package/pretty-time) [![NPM total downloads](https://img.shields.io/npm/dt/pretty-time.svg?style=flat)](https://npmjs.org/package/pretty-time) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/pretty-time.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/pretty-time)

> Easily format the time from node.js `process.hrtime`. Works with timescales ranging from weeks to nanoseconds.

Please consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save pretty-time
```

## Usage

```js
var pretty = require('pretty-time');

var start = process.hrtime();
var time = process.hrtime(start);
console.log(pretty(time));
//=> 3μs
```

## API

By default, when no time increment is given as the second argument, the closest timescale is used (e.g. _most granular without being less than zero_).

**Examples:**

```js
pretty([1200708, 795428088]);
//=> '2w'

pretty([800708, 795428088]);
//=> '1w'

pretty([400708, 795428088]);
//=> '5d'

pretty([70708, 795428088]);
//=> '20h'

pretty([12708, 795428088]);
//=> '4h'

pretty([3708, 795428088]);
//=> '1h'

pretty([208, 795428088]);
//=> '3m'

pretty([20, 795428088]);
//=> '21s'

pretty([0, 795428088]);
//=> '795ms'

pretty([0, 000428088]);
//=> '428μs'

pretty([0, 000000088]);
//=> '88ns'

pretty([0, 000000018]);
//=> '18ns'
```

### Minimum time increment

_(All of the following examples use `[6740, 795428088]` as the hrtime array.)_

This value is passed as the second argument and determines how granular to make the time.

**Examples**

```js
pretty(time, 'h');
//=> '2h'

pretty(time, 'm');
//=> '1h 52m'

pretty(time, 's');
//=> '1h 52m 21s'
```

**Valid time increments**

Any of the following may be used:

* `ns` | `nano` | `nanosecond` | `nanoseconds`
* `μs` | `micro` | `microsecond` | `microseconds`
* `ms` | `milli` | `millisecond` | `milliseconds`
* `s` | `sec` | `second` | `seconds`
* `m` | `min` | `minute` | `minutes`
* `h` | `hr` | `hour` | `hours`
* `d` | `day` | `days`
* `w` | `wk` | `week` | `weeks`

## About

<details>
<summary><strong>Contributing</strong></summary>

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

</details>

<details>
<summary><strong>Running Tests</strong></summary>

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

</details>

<details>
<summary><strong>Building docs</strong></summary>

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

</details>

### Related projects

You might also be interested in these projects:

* [o-clock](https://www.npmjs.com/package/o-clock): Simple javascript utility for displaying the time in 12-hour clock format. | [homepage](https://github.com/jonschlinkert/o-clock "Simple javascript utility for displaying the time in 12-hour clock format.")
* [seconds](https://www.npmjs.com/package/seconds): Get the number of seconds for a minute, hour, day and week. | [homepage](https://github.com/jonschlinkert/seconds "Get the number of seconds for a minute, hour, day and week.")
* [time-stamp](https://www.npmjs.com/package/time-stamp): Get a formatted timestamp. | [homepage](https://github.com/jonschlinkert/time-stamp "Get a formatted timestamp.")
* [timescale](https://www.npmjs.com/package/timescale): Convert from one time scale to another. Nanosecond is the most atomic unit, week is… [more](https://github.com/jonschlinkert/timescale) | [homepage](https://github.com/jonschlinkert/timescale "Convert from one time scale to another. Nanosecond is the most atomic unit, week is the largest unit.")
* [week](https://www.npmjs.com/package/week): Get the current week number. | [homepage](https://github.com/datetime/week "Get the current week number.")
* [weekday](https://www.npmjs.com/package/weekday): Get the name and number of the current weekday. Or get the name of the… [more](https://github.com/datetime/weekday) | [homepage](https://github.com/datetime/weekday "Get the name and number of the current weekday. Or get the name of the weekday for a given number.")
* [year](https://www.npmjs.com/package/year): Simple utility to get the current year with 2 or 4 digits. | [homepage](https://github.com/jonschlinkert/year "Simple utility to get the current year with 2 or 4 digits.")

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 14 | [jonschlinkert](https://github.com/jonschlinkert) |
| 5 | [doowb](https://github.com/doowb) |

### Author

**Jon Schlinkert**

* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)
* [GitHub Profile](https://github.com/jonschlinkert)
* [Twitter Profile](https://twitter.com/jonschlinkert)

### License

Copyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on July 12, 2018._