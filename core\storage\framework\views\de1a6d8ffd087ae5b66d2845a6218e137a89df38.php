<?php $__env->startSection('auth'); ?>
    <form action="<?php echo e(route('user.login')); ?>" class="account-form verify-gcaptcha" method="POST">
        <?php echo csrf_field(); ?>
        <div class="mb-4">
            <h4 class="mb-2"><?php echo app('translator')->get('Login to your account'); ?></h4>
            <p><?php echo app('translator')->get('You can sign in to your account using email or username'); ?></p>
        </div>
        <div class="row gy-2 gap-2">
            <div class="col-12">
                <div class="form-group">
                    <label><?php echo app('translator')->get('Username or Email'); ?> <span class="text--danger">*</span></label>
                    <input type="text" name="username" value="<?php echo e(old('username')); ?>" required class="form-control form--control h-45">
                </div>
            </div>
            <div class="col-12">
                <div class="form-group">
                    <label><?php echo app('translator')->get('Password'); ?> <span class="text--danger">*</span></label>
                    <input type="password" name="password" class="form-control form--control h-45" required>
                </div>
            </div>
            <div class="col-12">
                <div class="d-flex flex-wrap gap-2 justify-content-between">
                    <div class="form-group custom--checkbox">
                        <input type="checkbox" id="remember" name="remember" class="form-check-input" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                        <label for="remember"><?php echo app('translator')->get('Remember Me'); ?></label>
                    </div>
                    <a href="<?php echo e(route('user.password.request')); ?>" class="text--base fw-bold"><?php echo app('translator')->get('Forgot Password?'); ?></a>
                </div>
            </div>

            <?php if (isset($component)) { $__componentOriginalc0af13564821b3ac3d38dfa77d6cac9157db8243 = $component; } ?>
<?php $component = App\View\Components\Captcha::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('captcha'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Captcha::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc0af13564821b3ac3d38dfa77d6cac9157db8243)): ?>
<?php $component = $__componentOriginalc0af13564821b3ac3d38dfa77d6cac9157db8243; ?>
<?php unset($__componentOriginalc0af13564821b3ac3d38dfa77d6cac9157db8243); ?>
<?php endif; ?>

            <div class="col-12">
                <button type="submit" class="btn btn--base w-100"><?php echo app('translator')->get('Submit'); ?></button>
            </div>
            <div class="col-12">
                <p class="text-center">
                    <?php echo app('translator')->get('Don\'t have any account?'); ?>
                    <a href="<?php echo e(route('user.register')); ?>" class="fw-bold text--base"><?php echo app('translator')->get('Registration Here'); ?></a>
                </p>
            </div>
        </div>
    </form>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/user/auth/login.blade.php ENDPATH**/ ?>