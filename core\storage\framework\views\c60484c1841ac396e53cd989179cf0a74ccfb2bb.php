<div id="cronModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLongTitle"><?php echo app('translator')->get('Please Set Cron Job'); ?></h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="las la-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <div class="justify-content-between d-flex flex-wrap">
                        <label class="fw-bold"><?php echo app('translator')->get('Cron Command'); ?></label>
                        <small class="fst-italic">
                            <?php echo app('translator')->get('Last Cron Run'); ?>: <strong><?php echo e($general->last_cron ? diffForHumans($general->last_cron) : 'N/A'); ?></strong>
                        </small>
                    </div>
                    <div class="input-group">
                        <input type="text" class="form-control form-control-lg" id="cronPath" value="curl -s <?php echo e(route('cron')); ?>" readonly>
                        <button type="button" class="input-group-text copytext btn--primary border-primary copyCronPath"> <?php echo app('translator')->get('Copy'); ?></button>
                    </div>
                </div>
            </div>
            <div class="modal-footer justify-content-between d-flex flex-wrap">
                <p class="fst-italic">
                    <?php echo app('translator')->get('Once per 5-15 minutes is ideal while once every minute is the best option'); ?>
                    <u><a href="<?php echo e(route('cron.all')); ?>" type="button" class="text--warning underline"><?php echo app('translator')->get('Run manually'); ?></a></u>
                </p>
                <?php $hasPermission = App\Models\Role::hasPermission('admin.cron.index')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <a href="<?php echo e(route('admin.cron.index')); ?>">Manage Job Setting</a>
                <?php endif ?>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('script'); ?>
<script>
    (function($){
        "use strict";

        $(document).on('click', '.copyCronPath', function(){
            var copyText = document.getElementById('cronPath');

            copyText.select();
            copyText.setSelectionRange(0, 99999);
            
            document.execCommand('copy');
            notify('success', 'Copied: '+copyText.value);
        });
        
    })(jQuery)
</script>
<?php $__env->stopPush(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/partials/cron_modal.blade.php ENDPATH**/ ?>