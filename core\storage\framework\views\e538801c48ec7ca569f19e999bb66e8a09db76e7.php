<?php if(($cookie->data_values->status == 1) && !\Cookie::get('gdpr_cookie')): ?>
    <!-- cookies dark version start -->
    <div class="cookies-card text-center hide">
        <div class="cookies-card__icon bg--base">
            <i class="las la-cookie-bite"></i>
        </div>
        <p class="mt-4 cookies-card__content">
            <?php echo e($cookie->data_values->short_desc); ?> <a href="<?php echo e(route('cookie.policy')); ?>" target="_blank" class="text--primary"><?php echo app('translator')->get('learn more'); ?></a>
        </p>
        <div class="cookies-card__btn mt-4">
            <button type="button" class="btn btn--base w-100 policy"><?php echo app('translator')->get('Allow'); ?></button>
        </div>
    </div>
    <!-- cookies dark version end -->
<?php endif; ?>

<?php $__env->startPush('script'); ?>
<script> 
(function ($) { 
    "use strict";

    $('.policy').on('click',function(){
        $.get('<?php echo e(route('cookie.accept')); ?>', function(response){
            $('.cookies-card').addClass('d-none');
        });
    });

    setTimeout(function(){
        $('.cookies-card').removeClass('hide')
    },2000);
    
})(jQuery);
</script>
<?php $__env->stopPush(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/components/cookie-policy.blade.php ENDPATH**/ ?>