<?php $__env->startSection('content'); ?>
    <div class="col-lg-9">

        <?php if($user->kv == 0 || $user->kv == 2): ?>
            <?php
                $kyc = @getContent('kyc.content', true);
            ?>

            <div class="row">
                <div class="col-md-12">
                    <div class="card card custom--card style-two mb-4 mb-4 bg--navajowhite">
                        <div class="card-body">
                            <?php if($user->kv == 0): ?>
                                <div class="justify-content-between d-flex flex-wrap">
                                    <h6><?php echo app('translator')->get('KYC verification required'); ?></h6>
                                    <a href="<?php echo e(route('user.kyc.form')); ?>"><?php echo app('translator')->get('Click Here to Verify'); ?></a>
                                </div>
                                <hr>
                                <p><?php echo e(__(@$kyc->data_values->kyc_required)); ?></p>
                            <?php elseif($user->kv == 2): ?>
                                <?php
                                ?>
                                <div class="justify-content-between d-flex flex-wrap">
                                    <h6><?php echo app('translator')->get('KYC verification pending'); ?></h6>
                                    <a href="<?php echo e(route('user.kyc.data')); ?>"><?php echo app('translator')->get('Click Here to Data'); ?></a>
                                </div>
                                <hr>
                                <p><?php echo e(__(@$kyc->data_values->kyc_pending)); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="row user-dashboard">
            <div class="col-xl-4 col-md-6 col-sm-6">
                <div class="custom--card custom-radius-10 border-start border-0 border-3 border-left-primary">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div>
                                <p class="mb-0 text--secondary"><?php echo app('translator')->get('Balance'); ?></p>
                                <h4 class="my-1"><?php echo e($general->cur_sym); ?><?php echo e(showAmount($user->balance)); ?></h4>
                            </div>
                            <div class="widgets-icons-2 rounded--circle bg-gradient-blooker text--white ms--auto">
                                <i class="fas fa-wallet"></i>
                            </div>
                            <a href="<?php echo e(route('user.transactions')); ?>" class="has-anchor"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6 col-sm-6">
                <div class="custom--card custom-radius-10 border-start border-0 border-3 border-left-primary">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div>
                                <p class="mb-0 text--secondary"><?php echo app('translator')->get('Deposit'); ?></p>
                                <h4 class="my-1"><?php echo e(@$user->deposits->count()); ?></h4>
                            </div>
                            <div class="widgets-icons-2 rounded--circle bg-gradient-blooker text--white ms--auto">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <a href="<?php echo e(route('user.deposit.history')); ?>" class="has-anchor"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6 col-sm-6">
                <div class="custom--card custom-radius-10 border-start border-0 border-3 border-left-primary">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div>
                                <p class="mb-0 text--secondary"><?php echo app('translator')->get('Services'); ?></p>
                                <h4 class="my-1"><?php echo e($totalService); ?></h4>
                            </div>
                            <div class="widgets-icons-2 rounded--circle bg-gradient-scooter text--white ms--auto">
                                <i class="fa fa-box"></i>
                            </div>
                            <a href="<?php echo e(route('user.service.list')); ?>" class="has-anchor"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6 col-sm-6">
                <div class="custom--card custom-radius-10 border-start border-0 border-3 border-left-primary">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div>
                                <p class="mb-0 text--secondary"><?php echo app('translator')->get('Domains'); ?></p>
                                <h4 class="my-1"><?php echo e($totalDomain); ?></h4>
                            </div>
                            <div class="widgets-icons-2 rounded--circle bg-gradient-bloody text--white ms--auto">
                                <i class="fas fa-globe"></i>
                            </div>
                            <a href="<?php echo e(route('user.domain.list')); ?>" class="has-anchor"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6 col-sm-6">
                <div class="custom--card custom-radius-10 border-start border-0 border-3 border-left-primary">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div>
                                <p class="mb-0 text--secondary"><?php echo app('translator')->get('Tickets'); ?>

                                </p>
                                <h4 class="my-1"><?php echo e($totalTicket); ?></h4>
                            </div>
                            <div class="widgets-icons-2 rounded--circle bg-gradient-ohhappiness text--white ms--auto">
                                <i class="fa fa-comments"></i>
                            </div>
                            <a href="<?php echo e(route('ticket.index')); ?>" class="has-anchor"></a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-md-6 col-sm-6">
                <div class="custom--card custom-radius-10 border-start border-0 border-3 border-left-primary">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div>
                                <p class="mb-0 text--secondary"><?php echo app('translator')->get('Invoices'); ?></p>
                                <h4 class="my-1"><?php echo e($totalInvoice); ?></h4>
                            </div>
                            <div class="widgets-icons-2 rounded--circle bg-gradient-blooker text--white ms--auto">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <a href="<?php echo e(route('user.invoice.list')); ?>" class="has-anchor"></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-xl-6 col-lg-6">
                <div class="card custom-border-top-dark">
                    <div class="card-body">
                        <div class="justify-content-between d-flex mb-3 flex-wrap">
                            <div> <i class="fas fa-calculator"></i> <?php echo app('translator')->get('Overdue Invoices'); ?> </div>
                            <a class="btn btn--xs btn--base" href="<?php echo e(route('user.invoice.list')); ?>">
                                <i class="fas fa-list"></i> <?php echo app('translator')->get('View All'); ?>
                            </a>
                        </div>
                        <?php echo app('translator')->get('You have'); ?> <?php echo e($totalOverDueInvoice->total); ?>

                        <?php echo app('translator')->get('overdue invoice(s) with a total balance due of'); ?>
                        <?php echo e($general->cur_sym); ?><?php echo e(showAmount($totalOverDueInvoice->totalDue)); ?>

                        <?php echo e(__($general->cur_text)); ?>.
                        <?php echo app('translator')->get('Pay them now to avoid any interruptions in service'); ?>.
                    </div>
                </div>
            </div>
            <div class="col-xl-6 col-lg-6">
                <div class="card custom-border-top-dark h-100">
                    <div class="card-body">
                        <div class="justify-content-between d-flex mb-3 flex-wrap">
                            <div> <i class="fas fa-cube"></i> <?php echo app('translator')->get('Products/Services'); ?> </div>
                            <a class="btn btn--xs btn--base" href="<?php echo e(route('user.service.list')); ?>"> <i class="fas fa-list"></i> <?php echo app('translator')->get('View All'); ?></a>
                        </div>
                        <?php echo app('translator')->get('It appears you do not have any products/services with us yet'); ?>.
                        <a href="<?php echo e(route('service.category')); ?>?all"><?php echo app('translator')->get('Place an order to get started'); ?></a>.
                    </div>
                </div>
            </div>
            <div class="col-xl-6 col-lg-6">
                <div class="card custom-border-top-dark h-100">
                    <div class="card-body">
                        <div class="justify-content-between d-flex mb-3 flex-wrap">
                            <div> <i class="fas fa-comments"></i> <?php echo app('translator')->get('Support Tickets'); ?> </div>
                            <a class="btn btn--xs btn--base" href="<?php echo e(route('ticket.index')); ?>"> <i class="fas fa-list"></i> <?php echo app('translator')->get('View All'); ?></a>
                        </div>
                        <?php echo app('translator')->get('No Recent Tickets Found. If you need any help'); ?>, <a href="<?php echo e(route('ticket.open')); ?>"><?php echo app('translator')->get('please open a ticket'); ?></a>.
                    </div>
                </div>
            </div>
            <div class="col-xl-6 col-lg-6">
                <div class="card custom-border-top-dark h-100">
                    <div class="card-body">
                        <div> <i class="fas fa-globe"></i> <?php echo app('translator')->get('Register New Domain'); ?> </div>
                        <form action="" class="form mt-4">
                            <div class="form-group position-relative mb-0">
                                <div class="domain-search-icon"><i class="fas fa-search"></i></div>
                                <input class="form-control form--control h-45" type="text" name="domain" placeholder="<?php echo app('translator')->get('Domain name or keyword'); ?>" required>
                                <div class="domain-search-icon-reset">
                                    <button class="btn btn--base btn--sm" type="submit"><?php echo app('translator')->get('Search'); ?></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        (function($) {
            "use strict";
            $('.form').on('submit', function(e) {
                e.preventDefault();
                var domain = $(this).find('input[name=domain]').val();
                window.location.href = "<?php echo e(route('register.domain')); ?>?domain=" + domain;
            })
        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.master_side_bar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/user/dashboard.blade.php ENDPATH**/ ?>