{"name": "postcss-normalize-positions", "version": "5.0.4", "description": "Normalize keyword values for position into length values.", "main": "src/index.js", "files": ["src", "LICENSE-MIT"], "keywords": ["css", "postcss", "postcss-plugin"], "license": "MIT", "homepage": "https://github.com/cssnano/cssnano", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "dependencies": {"postcss-value-parser": "^4.2.0"}, "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "devDependencies": {"postcss": "^8.2.15"}, "peerDependencies": {"postcss": "^8.2.15"}, "readme": "# [postcss][postcss]-normalize-positions\n\n> Normalize positions with PostCSS.\n\n## Install\n\nWith [npm](https://npmjs.org/package/postcss-normalize-positions) do:\n\n```\nnpm install postcss-normalize-positions --save\n```\n\n## Example\n\n### Input\n\n```css\ndiv {\n    background-position: bottom left;\n}\n```\n\n### Output\n\n```css\ndiv {\n    background-position:0 100%;\n}\n``` \n\n## Usage\n\nSee the [PostCSS documentation](https://github.com/postcss/postcss#usage) for\nexamples for your environment.\n\n## Contributors\n\nSee [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).\n\n## License\n\nMIT © [<PERSON>](http://beneb.info)\n\n[postcss]: https://github.com/postcss/postcss\n"}