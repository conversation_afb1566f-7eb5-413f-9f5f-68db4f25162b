{"name": "imagemin", "version": "7.0.1", "description": "Minify images seamlessly", "license": "MIT", "repository": "imagemin/imagemin", "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["minify", "compress", "image", "images", "jpeg", "jpg", "png", "gif", "svg"], "dependencies": {"file-type": "^12.0.0", "globby": "^10.0.0", "graceful-fs": "^4.2.2", "junk": "^3.1.0", "make-dir": "^3.0.0", "p-pipe": "^3.0.0", "replace-ext": "^1.0.0"}, "devDependencies": {"ava": "^2.1.0", "del": "^4.1.1", "imagemin-jpegtran": "^6.0.0", "imagemin-svgo": "^7.0.0", "imagemin-webp": "^5.0.0", "is-jpg": "^2.0.0", "tempy": "^0.3.0", "xo": "^0.24.0"}}