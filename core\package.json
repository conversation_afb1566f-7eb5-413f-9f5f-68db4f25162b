{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "legacy:dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production"}, "dependencies": {"@tanstack/react-query": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "zustand": "^4.4.0", "axios": "^1.6.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.16", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.0.2", "vite": "^5.0.0", "@popperjs/core": "^2.10.2", "bootstrap": "^5.1.3", "laravel-mix": "^6.0.6", "lodash": "^4.17.19", "resolve-url-loader": "^5.0.0", "sass": "^1.32.11", "sass-loader": "^11.0.1"}}