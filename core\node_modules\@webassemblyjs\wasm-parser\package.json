{"name": "@webassemblyjs/wasm-parser", "version": "1.11.1", "keywords": ["webassembly", "javascript", "ast", "parser", "wasm"], "description": "WebAssembly binary format parser", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "mocha"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/utf8": "1.11.1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "devDependencies": {"@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-test-framework": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.7.7", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wast-parser": "1.11.1", "mamacro": "^0.0.7", "wabt": "1.0.12"}, "gitHead": "3f07e2db2031afe0ce686630418c542938c1674b"}