<div class="sidebar bg--dark">
    <button class="res-sidebar-close-btn"><i class="las la-times"></i></button>
    <div class="sidebar__inner">
        <div class="sidebar__logo">
            <a href="<?php echo e(route('admin.dashboard')); ?>" class="sidebar__main-logo"><img src="<?php echo e(getImage(getFilePath('logoIcon') .'/logo.png')); ?>" alt="<?php echo app('translator')->get('image'); ?>"></a>
        </div>
 
        <div class="sidebar__menu-wrapper" id="sidebar__menuWrapper">
            <ul class="sidebar__menu">

                <?php $hasPermission = App\Models\Role::hasPermission('admin.dashboard')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.dashboard')); ?>">
                        <a href="<?php echo e(route('admin.dashboard')); ?>" class="nav-link ">
                            <i class="menu-icon las la-home"></i>
                            <span class="menu-title"><?php echo app('translator')->get('Dashboard'); ?></span>
                        </a>
                    </li>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission(['admin.staff.index', 'admin.roles.index', 'admin.permissions.index'])  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <li class="sidebar-menu-item sidebar-dropdown"> 
                        <a class="<?php echo e(menuActive(['admin.staff*', 'admin.roles.*'], 3)); ?>" href="javascript:void(0)">
                            <i class="menu-icon las la-user-friends"></i>
                            <span class="menu-title"><?php echo app('translator')->get('Manage Staff'); ?></span>
                        </a>
                        <div class="sidebar-submenu <?php echo e(menuActive(['admin.staff*', 'admin.roles.*', 'admin.permissions*'], 2)); ?>">
                            <ul>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.staff.index')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.staff*')); ?>">
                                        <a class="nav-link" href="<?php echo e(route('admin.staff.index')); ?>">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('All Staff'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.roles.index')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.roles*')); ?>">
                                        <a class="nav-link" href="<?php echo e(route('admin.roles.index')); ?>">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Roles'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                            </ul>
                        </div>
                    </li>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission([
                    'admin.users.active', 'admin.users.banned', 'admin.users.email.unverified', 'admin.users.mobile.unverified', 
                    'admin.users.kyc.unverified', 'admin.users.kyc.pending', 'admin.users.with.balance', 'admin.users.all', 'admin.users.notification.all'
                ])  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <li class="sidebar-menu-item sidebar-dropdown">
                        <a href="javascript:void(0)" class="<?php echo e(menuActive('admin.users*',3)); ?>">
                            <i class="menu-icon las la-users"></i>
                            <span class="menu-title"><?php echo app('translator')->get('Manage Clients'); ?></span>

                            <?php if($bannedUsersCount > 0 || $emailUnverifiedUsersCount > 0 || $mobileUnverifiedUsersCount > 0 || $kycUnverifiedUsersCount > 0 || $kycPendingUsersCount > 0): ?>
                                <span class="menu-badge pill bg--danger ms-auto">
                                    <i class="fa fa-exclamation"></i>
                                </span>
                            <?php endif; ?>
                        </a>
                        <div class="sidebar-submenu <?php echo e(menuActive('admin.users*',2)); ?> ">
                            <ul>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.users.active')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.users.active')); ?> ">
                                        <a href="<?php echo e(route('admin.users.active')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Active Clients'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.users.banned')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.users.banned')); ?> ">
                                        <a href="<?php echo e(route('admin.users.banned')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Banned Clients'); ?></span>
                                            <?php if($bannedUsersCount): ?>
                                                <span class="menu-badge pill bg--danger ms-auto"><?php echo e($bannedUsersCount); ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.users.email.unverified')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item  <?php echo e(menuActive('admin.users.email.unverified')); ?>">
                                        <a href="<?php echo e(route('admin.users.email.unverified')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Email Unverified'); ?></span>

                                            <?php if($emailUnverifiedUsersCount): ?>
                                                <span class="menu-badge pill bg--danger ms-auto"><?php echo e($emailUnverifiedUsersCount); ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.users.mobile.unverified')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.users.mobile.unverified')); ?>">
                                        <a href="<?php echo e(route('admin.users.mobile.unverified')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Mobile Unverified'); ?></span>
                                            <?php if($mobileUnverifiedUsersCount): ?>
                                                <span
                                                    class="menu-badge pill bg--danger ms-auto"><?php echo e($mobileUnverifiedUsersCount); ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.users.kyc.unverified')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.users.kyc.unverified')); ?>">
                                        <a href="<?php echo e(route('admin.users.kyc.unverified')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('KYC Unverified'); ?></span>
                                            <?php if($kycUnverifiedUsersCount): ?>
                                                <span class="menu-badge pill bg--danger ms-auto"><?php echo e($kycUnverifiedUsersCount); ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.users.kyc.pending')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.users.kyc.pending')); ?>">
                                        <a href="<?php echo e(route('admin.users.kyc.pending')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('KYC Pending'); ?></span>
                                            <?php if($kycPendingUsersCount): ?>
                                                <span class="menu-badge pill bg--danger ms-auto"><?php echo e($kycPendingUsersCount); ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.users.with.balance')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.users.with.balance')); ?>">
                                        <a href="<?php echo e(route('admin.users.with.balance')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('With Balance'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.users.all')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.users.all')); ?> ">
                                        <a href="<?php echo e(route('admin.users.all')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('All Clients'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.users.notification.all')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.users.notification.all')); ?>">
                                        <a href="<?php echo e(route('admin.users.notification.all')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Notification to All'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                            </ul>
                        </div>
                    </li>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission(['admin.deposit.pending', 'admin.deposit.approved', 'admin.deposit.successful', 'admin.deposit.rejected', 'admin.deposit.initiated', 'admin.deposit.list'])  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <li class="sidebar-menu-item sidebar-dropdown">
                        <a href="javascript:void(0)" class="<?php echo e(menuActive('admin.deposit*',3)); ?>">
                            <i class="menu-icon las la-file-invoice-dollar"></i>
                            <span class="menu-title"><?php echo app('translator')->get('Payments'); ?></span>
                            <?php if(0 < $pendingDepositsCount): ?>
                                <span class="menu-badge pill bg--danger ms-auto">
                                    <i class="fa fa-exclamation"></i>
                                </span>
                            <?php endif; ?>
                        </a>
                        <div class="sidebar-submenu <?php echo e(menuActive('admin.deposit*',2)); ?> ">
                            <ul>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.deposit.pending')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.deposit.pending')); ?> ">
                                        <a href="<?php echo e(route('admin.deposit.pending')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Pending Payments'); ?></span>
                                            <?php if($pendingDepositsCount): ?>
                                                <span class="menu-badge pill bg--danger ms-auto"><?php echo e($pendingDepositsCount); ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.deposit.approved')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.deposit.approved')); ?> ">
                                        <a href="<?php echo e(route('admin.deposit.approved')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Approved Payments'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.deposit.successful')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.deposit.successful')); ?> ">
                                        <a href="<?php echo e(route('admin.deposit.successful')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Successful Payments'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.deposit.rejected')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.deposit.rejected')); ?> ">
                                        <a href="<?php echo e(route('admin.deposit.rejected')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Rejected Payments'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.deposit.initiated')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.deposit.initiated')); ?> ">
                                        <a href="<?php echo e(route('admin.deposit.initiated')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Initiated Payments'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.deposit.list')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.deposit.list')); ?> ">
                                        <a href="<?php echo e(route('admin.deposit.list')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('All Payments'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                            </ul>
                        </div>
                    </li>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission(['admin.orders.active', 'admin.orders.pending', 'admin.orders.cancelled', 'admin.orders'])  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <li class="sidebar-menu-item sidebar-dropdown">
                        <a href="javascript:void(0)" class="<?php echo e(menuActive('admin.orders*',3)); ?>">
                            <i class="menu-icon la la-shopping-bag"></i>
                            <span class="menu-title"><?php echo app('translator')->get('Orders'); ?> </span>
                            <?php if(0 < $pendingOrderCount): ?>
                                <span class="menu-badge pill bg--danger ms-auto">
                                    <i class="fa fa-exclamation"></i>
                                </span>
                            <?php endif; ?>
                        </a>
                        <div class="sidebar-submenu <?php echo e(menuActive('admin.orders*',2)); ?> ">
                            <ul>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.orders.active')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.orders.active')); ?> ">
                                        <a href="<?php echo e(route('admin.orders.active')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Active Orders'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.orders.pending')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.orders.pending')); ?> ">
                                        <a href="<?php echo e(route('admin.orders.pending')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Pending Orders'); ?></span>
                                            <?php if($pendingOrderCount): ?>
                                                <span class="menu-badge pill bg--danger ms-auto"><?php echo e($pendingOrderCount); ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.orders.cancelled')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.orders.cancelled')); ?> ">
                                        <a href="<?php echo e(route('admin.orders.cancelled')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Cancelled Orders'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.orders')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.orders')); ?> ">
                                        <a href="<?php echo e(route('admin.orders')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('All Orders'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                            </ul>
                        </div>
                    </li>
                <?php endif ?>
                
                <?php $hasPermission = App\Models\Role::hasPermission(['admin.invoices.paid', 'admin.invoices.unpaid', 'admin.invoices.payment.pending', 'admin.invoices.cancelled', 'admin.invoices.refunded', 'admin.invoices'])  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <li class="sidebar-menu-item sidebar-dropdown">
                        <a href="javascript:void(0)" class="<?php echo e(menuActive('admin.invoices*',3)); ?>">
                            <i class="menu-icon la la-file-invoice"></i>
                            <span class="menu-title"><?php echo app('translator')->get('Invoices'); ?> </span>
                            <?php if(0 < $unpaidInvoiceCount): ?>
                                <span class="menu-badge pill bg--danger ms-auto">
                                    <i class="fa fa-exclamation"></i>
                                </span>
                            <?php endif; ?>
                        </a>
                        <div class="sidebar-submenu <?php echo e(menuActive('admin.invoices*',2)); ?> ">
                            <ul>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.invoices.paid')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.invoices.paid')); ?> ">
                                        <a href="<?php echo e(route('admin.invoices.paid')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Paid Invoices'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.invoices.unpaid')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.invoices.unpaid')); ?> ">
                                        <a href="<?php echo e(route('admin.invoices.unpaid')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Unpaid Invoices'); ?></span>
                                            <?php if($unpaidInvoiceCount): ?>
                                                <span class="menu-badge pill bg--danger ms-auto"><?php echo e($unpaidInvoiceCount); ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.invoices.payment.pending')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.invoices.payment.pending')); ?> ">
                                        <a href="<?php echo e(route('admin.invoices.payment.pending')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Payment Pending'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.invoices.cancelled')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.invoices.cancelled')); ?> ">
                                        <a href="<?php echo e(route('admin.invoices.cancelled')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Cancelled Invoices'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.invoices.refunded')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.invoices.refunded')); ?> ">
                                        <a href="<?php echo e(route('admin.invoices.refunded')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Refunded Invoices'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.invoices')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.invoices')); ?> ">
                                        <a href="<?php echo e(route('admin.invoices')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('All Invoices'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                            </ul>
                        </div>
                    </li>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission(['admin.cancel.request.pending', 'admin.cancel.request.completed', 'admin.cancel.requests'])  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <li class="sidebar-menu-item sidebar-dropdown">
                        <a href="javascript:void(0)" class="<?php echo e(menuActive('admin.cancel.request*',3)); ?>">
                            <i class="menu-icon la la-ban"></i>
                            <span class="menu-title"><?php echo app('translator')->get('Cancellation'); ?> </span>
                            <?php if(0 < $pendingCancelRequestCount): ?>
                                <span class="menu-badge pill bg--danger ms-auto">
                                    <i class="fa fa-exclamation"></i>
                                </span>
                            <?php endif; ?>
                        </a>
                        <div class="sidebar-submenu <?php echo e(menuActive('admin.cancel.request*',2)); ?> ">
                            <ul>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.cancel.request.pending')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.cancel.request.pending')); ?> ">
                                        <a href="<?php echo e(route('admin.cancel.request.pending')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Pending Requests'); ?></span>
                                            <?php if($pendingCancelRequestCount): ?>
                                                <span class="menu-badge pill bg--danger ms-auto"><?php echo e($pendingCancelRequestCount); ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.cancel.request.completed')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.cancel.request.completed')); ?> ">
                                        <a href="<?php echo e(route('admin.cancel.request.completed')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Completed Requests'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.cancel.requests')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.cancel.requests')); ?> ">
                                        <a href="<?php echo e(route('admin.cancel.requests')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('All Requests'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                            </ul>
                        </div>
                    </li>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission(['admin.ticket.pending', 'admin.ticket.closed', 'admin.ticket.answered', 'admin.ticket.index'])  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <li class="sidebar-menu-item sidebar-dropdown">
                        <a href="javascript:void(0)" class="<?php echo e(menuActive('admin.ticket*',3)); ?>">
                            <i class="menu-icon la la-ticket"></i>
                            <span class="menu-title"><?php echo app('translator')->get('Support Ticket'); ?> </span>
                            <?php if(0 < $pendingTicketCount): ?>
                                <span class="menu-badge pill bg--danger ms-auto">
                                    <i class="fa fa-exclamation"></i>
                                </span>
                            <?php endif; ?>
                        </a>
                        <div class="sidebar-submenu <?php echo e(menuActive('admin.ticket*',2)); ?> ">
                            <ul>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.ticket.pending')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.ticket.pending')); ?> ">
                                        <a href="<?php echo e(route('admin.ticket.pending')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Pending Ticket'); ?></span>
                                            <?php if($pendingTicketCount): ?>
                                            <span
                                            class="menu-badge pill bg--danger ms-auto"><?php echo e($pendingTicketCount); ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.ticket.closed')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.ticket.closed')); ?> ">
                                        <a href="<?php echo e(route('admin.ticket.closed')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Closed Ticket'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.ticket.answered')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.ticket.answered')); ?> ">
                                        <a href="<?php echo e(route('admin.ticket.answered')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Answered Ticket'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.ticket.index')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.ticket.index')); ?> ">
                                        <a href="<?php echo e(route('admin.ticket.index')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('All Ticket'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                            </ul>
                        </div>
                    </li>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission(['admin.report.transaction', 'admin.report.login.history', 'admin.report.notification.history'])  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <li class="sidebar-menu-item sidebar-dropdown">
                        <a href="javascript:void(0)" class="<?php echo e(menuActive('admin.report*',3)); ?>">
                            <i class="menu-icon la la-list"></i>
                            <span class="menu-title"><?php echo app('translator')->get('Report'); ?> </span>
                        </a>
                        <div class="sidebar-submenu <?php echo e(menuActive('admin.report*',2)); ?> ">
                            <ul>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.report.transaction')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive(['admin.report.transaction','admin.report.transaction.search'])); ?>">
                                        <a href="<?php echo e(route('admin.report.transaction')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Transaction Log'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.report.login.history')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive(['admin.report.login.history','admin.report.login.ipHistory'])); ?>">
                                        <a href="<?php echo e(route('admin.report.login.history')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Login History'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                                <?php $hasPermission = App\Models\Role::hasPermission('admin.report.notification.history')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                    <li class="sidebar-menu-item <?php echo e(menuActive('admin.report.notification.history')); ?>">
                                        <a href="<?php echo e(route('admin.report.notification.history')); ?>" class="nav-link">
                                            <i class="menu-icon las la-dot-circle"></i>
                                            <span class="menu-title"><?php echo app('translator')->get('Notification History'); ?></span>
                                        </a>
                                    </li>
                                <?php endif ?>
                            </ul>
                        </div>
                    </li>
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission('admin.system.setting')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <?php
                        $active = [
                            'admin.system*', 'admin.setting*', 'admin.billing.setting', 'admin.service.category', 
                            'admin.configurable*', 'admin.server*', 'admin.groups.server', 'admin.tld', 'admin.register.domain',
                            'admin.products', 'admin.product*', 'admin.coupons', 'admin.extensions.index', 
                            'admin.language.manage', 'admin.seo', 'admin.kyc.setting', 'admin.subscriber.index', 'admin.maintenance.mode','admin.gateway*', 'admin.request.report', 'admin.cron*'
                        ];
                    ?> 
                    <li class="sidebar__menu-header"><?php echo app('translator')->get('Settings'); ?></li>
                    <li class="sidebar-menu-item <?php echo e(menuActive($active)); ?>">
                        <a href="<?php echo e(route('admin.system.setting')); ?>" class="nav-link">
                            <i class="menu-icon las la-cog"></i>
                            <span class="menu-title"><?php echo app('translator')->get('System Setting'); ?></span>
                        </a>
                    </li>  
                <?php endif ?>

                <?php $hasPermission = App\Models\Role::hasPermission(['admin.frontend.templates', 'admin.frontend.sections*'])  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <li class="sidebar__menu-header"><?php echo app('translator')->get('Frontend'); ?></li>

                    <?php $hasPermission = App\Models\Role::hasPermission('admin.frontend.templates')  ? 1 : 0;
            if($hasPermission == 1): ?>
                        <li class="sidebar-menu-item <?php echo e(menuActive(['admin.frontend.templates*'])); ?>">
                            <a href="<?php echo e(route('admin.frontend.templates')); ?>" class="nav-link ">
                                <i class="menu-icon la la-html5"></i>
                                <span class="menu-title"><?php echo app('translator')->get('Manage Templates'); ?></span>
                            </a>
                        </li>
                    <?php endif ?> 
                    <?php $hasPermission = App\Models\Role::hasPermission('admin.frontend.sections')  ? 1 : 0;
            if($hasPermission == 1): ?>
                        <li class="sidebar-menu-item sidebar-dropdown">
                            <a href="javascript:void(0)" class="<?php echo e(menuActive('admin.frontend.sections*',3)); ?>">
                                <i class="menu-icon la la-puzzle-piece"></i>
                                <span class="menu-title"><?php echo app('translator')->get('Manage Section'); ?></span>
                            </a>
                            <div class="sidebar-submenu <?php echo e(menuActive('admin.frontend.sections*',2)); ?> ">
                                <ul>
                                    <?php
                                    $lastSegment =  collect(request()->segments())->last();
                                    ?>
                                    <?php $__currentLoopData = getPageSections(true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $secs): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($secs['builder']): ?>
                                            <li class="sidebar-menu-item  <?php if($lastSegment == $k): ?> active <?php endif; ?> ">
                                                <a href="<?php echo e(route('admin.frontend.sections',$k)); ?>" class="nav-link">
                                                    <i class="menu-icon las la-dot-circle"></i>
                                                    <span class="menu-title"><?php echo e(__($secs['name'])); ?></span>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        </li>
                    <?php endif ?>
                <?php endif ?>
 
                <?php $hasPermission = App\Models\Role::hasPermission(['admin.automation.errors', 'admin.system.update'])  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <li class="sidebar__menu-header"><?php echo app('translator')->get('Others'); ?></li>

                    <?php $hasPermission = App\Models\Role::hasPermission('admin.automation.errors')  ? 1 : 0;
            if($hasPermission == 1): ?>
                        <li class="sidebar-menu-item  <?php echo e(menuActive('admin.automation.errors')); ?>">
                            <a href="<?php echo e(route('admin.automation.errors')); ?>" class="nav-link" data-default-url="<?php echo e(route('admin.automation.errors')); ?>">
                                <i class="menu-icon las la-exclamation-triangle"></i>
                                <span class="menu-title"><?php echo app('translator')->get('Automation Errors'); ?> </span>
                                <?php if($countAutomationError): ?>
                                    <span class="menu-badge pill bg--danger ms-auto"><?php echo e($countAutomationError); ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                    <?php endif ?>
                    <?php $hasPermission = App\Models\Role::hasPermission('admin.system.update')  ? 1 : 0;
            if($hasPermission == 1): ?>
                        <li class="sidebar-menu-item <?php echo e(menuActive('admin.system.update')); ?> ">
                            <a href="<?php echo e(route('admin.system.update')); ?>" class="nav-link">
                                <i class="menu-icon las la-dot-circle"></i>
                                <span class="menu-title"><?php echo app('translator')->get('System Update'); ?></span>
                            </a>
                        </li>
                    <?php endif ?>
                <?php endif ?>

            </ul>
            <div class="text-center mb-3 text-uppercase">
                <span class="text--primary"><?php echo e(__(systemDetails()['name'])); ?></span>
                <span class="text--success"><?php echo app('translator')->get('V'); ?><?php echo e(systemDetails()['version']); ?> </span>
            </div>
        </div>
    </div>
</div>
<!-- sidebar end -->

<?php $__env->startPush('script'); ?>
    <script>
        if($('li').hasClass('active')){
            $('#sidebar__menuWrapper').animate({
                scrollTop: eval($(".active").offset().top - 320)
            },500);
        }
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/partials/sidenav.blade.php ENDPATH**/ ?>