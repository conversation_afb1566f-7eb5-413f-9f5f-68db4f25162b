<?php $__env->startSection('data'); ?>
    <div class="col-lg-9">
        <div class="row g-3">
            <div class="col-lg-8">
                <h4><?php echo app('translator')->get('Cart'); ?></h4>
                <?php $__empty_1 = true; $__currentLoopData = $carts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cart): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="card fs-12 cart_child m-1 mt-2">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-11 d-flex justify-content-between flex-wrap align-items-center">
                                    <?php if($cart->product_id && !$cart->domain_setup_id && !$cart->domain_id): ?>
                                        <div>
                                            <h6 class="d-inline"><?php echo e(__($cart->product->name)); ?></h6>
                                            <a href="<?php echo e(route('shopping.cart.config.service', $cart->id)); ?>">
                                                <i class="la la-pencil"></i> <?php echo app('translator')->get('Edit'); ?>
                                            </a>
                                            <span class="d-block"><?php echo e(__($cart->product->serviceCategory->name)); ?></span>
                                            <span class="d-block fw-bold"><?php echo e(@$cart->domain); ?></span>
                                        </div>
                                        <div class="mt-1 mt-lg-0">
                                            <h6 class="d-inline"><?php echo e($general->cur_sym); ?><?php echo e(showAmount(@$cart->price)); ?> <?php echo e(__($general->cur_text)); ?></h6>
                                            <span class="d-block"><?php echo e(@billingCycle($cart->billing_cycle, true)['showText']); ?></span>
                                            <span class="d-block small"><?php echo e($general->cur_sym); ?><?php echo e(showAmount(@$cart->setup_fee)); ?> <?php echo app('translator')->get('Setup Fee'); ?></span>
                                            <span class="fst-italic fw-bold small">
                                                <?php echo app('translator')->get('Total'); ?> <?php echo e(@$general->cur_sym); ?><?php echo e(showAmount(@$cart->total)); ?> <?php echo e(__($general->cur_text)); ?>

                                            </span>
                                        </div>
                                    <?php else: ?>
                                        <div>
                                            <?php if($cart->type == 4): ?>
                                                <h6 class="d-inline"><?php echo app('translator')->get('Domain Renew'); ?></h6>
                                                <a href="<?php echo e(route('user.domain.details', $cart->domain_id)); ?>">
                                                    <i class="la la-pencil"></i> <?php echo app('translator')->get('Edit'); ?>
                                                </a>
                                            <?php else: ?>
                                                <h6 class="d-inline"><?php echo app('translator')->get('Domain Registration'); ?></h6>
                                                <a href="<?php echo e(route('shopping.cart.config.domain', $cart->id)); ?>">
                                                    <i class="la la-pencil"></i> <?php echo app('translator')->get('Edit'); ?>
                                                </a>
                                            <?php endif; ?>
                                            <span class="d-block fw-bold">
                                                <?php echo e(@$cart->domain); ?> - <?php echo e(@$cart->reg_period); ?> <?php echo app('translator')->get('Year'); ?>
                                                <?php echo e(@$cart->id_protection ? __('with ID Protection') : null); ?>

                                            </span>
                                        </div>
                                        <div class="mt-1 mt-lg-0">
                                            <h6 class="d-block"><?php echo e($general->cur_sym); ?><?php echo e(showAmount(@$cart->price)); ?> <?php echo e(__($general->cur_text)); ?></h6>
                                            <?php if(@$cart->id_protection): ?>
                                                <span class="d-block small"><?php echo e($general->cur_sym); ?><?php echo e(showAmount(@$cart->setup_fee)); ?> <?php echo app('translator')->get('ID Protection'); ?></span>
                                            <?php endif; ?>
                                            <span class="fst-italic fw-bold small">
                                                <?php echo app('translator')->get('Total'); ?> <?php echo e(@$general->cur_sym); ?><?php echo e(showAmount(@$cart->total)); ?> <?php echo e(__($general->cur_text)); ?>

                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-1 form-group">
                                    <a class="remove_cart d-none" href="<?php echo e(route('shopping.cart.remove', $cart->id)); ?>">
                                        <i class="la la-trash">&nbsp;<?php echo app('translator')->get('Remove'); ?></i>
                                    </a>
                                    <a href="<?php echo e(route('shopping.cart.remove', $cart->id)); ?>" class="remove_icon">
                                        <i class="la la-times"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="row">
                        <div class="col-md-12 text-center mt-3">
                            <div class="card p-4">
                                <?php if (isset($component)) { $__componentOriginal5ee599e831bdc2bb496580d5cedaf21b3c64fbde = $component; } ?>
<?php $component = App\View\Components\EmptyMessage::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('empty-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\EmptyMessage::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['div' => '{{','true' => true,'message' => 'Empty carts']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5ee599e831bdc2bb496580d5cedaf21b3c64fbde)): ?>
<?php $component = $__componentOriginal5ee599e831bdc2bb496580d5cedaf21b3c64fbde; ?>
<?php unset($__componentOriginal5ee599e831bdc2bb496580d5cedaf21b3c64fbde); ?>
<?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if(@$cart): ?>
                    <div class="row">
                        <div class="col-lg-12 text-end mb-4">
                            <span class="bg--dark text-white p-2 fs-12 me-1 btn--xs btn">
                                <a href="<?php echo e(route('shopping.cart.empty')); ?>" class="text-white"><i class="la la-trash"></i> <?php echo app('translator')->get('Empty Cart'); ?></a>
                            </span>
                        </div>
                        <div class="col-lg-12">
                            <div class="card p-3">
                                <?php if(@$appliedCoupon): ?>
                                    <form action="<?php echo e(route('shopping.cart.coupon.remove')); ?>" method="post">
                                        <?php echo csrf_field(); ?>
                                        <p class="border p-2 text-center">
                                            <?php echo e($appliedCoupon->coupon->code); ?> - <?php echo e($appliedCoupon->coupon_type == 0 ? showAmount($appliedCoupon->coupon_discount) . '%' : showAmount($carts->sum('discount')) . ' ' . $general->cur_text); ?> <?php echo app('translator')->get('Discount'); ?>
                                        </p>
                                        <div class="form-group mt-2">
                                            <button type="submit" class="btn btn-warning w-100 text-white"><?php echo app('translator')->get('Remove Coupon Code'); ?></button>
                                        </div>
                                    </form>
                                <?php else: ?>
                                    <form action="<?php echo e(route('shopping.cart.coupon')); ?>" method="post">
                                        <?php echo csrf_field(); ?>
                                        <div class="form-group">
                                            <input type="text" class="form-control form--control h-45" name="coupon_code" placeholder="<?php echo app('translator')->get('Enter coupon code if you have one'); ?>" required>
                                        </div>
                                        <div class="form-group mt-2">
                                            <button type="submit" class="btn btn--base btn--sm w-100"><?php echo app('translator')->get('Validate Code'); ?></button>
                                        </div>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="col-lg-4">
                <div class="card p-3">
                    <span class="border-bottom p-2 bg-dark-two text-center fw-bold w-100"><?php echo app('translator')->get('Order Summary'); ?></span>
                    <div class="card-body pb-0 px-0">
                        <div>
                            <div class="d-flex justify-content-between mt-3">
                                <span><?php echo app('translator')->get('Subtotal'); ?></span>
                                <span><?php echo e(@$general->cur_sym); ?><span class="basicPrices"><?php echo e($carts->sum('total')); ?></span> <?php echo e(__(@$general->cur_text)); ?></span>
                            </div>
                        </div>
                        <?php if($appliedCoupon): ?>
                            <div class="border-top mt-3">
                                <div class="d-flex justify-content-between small mt-1">
                                    <span class="discounts">
                                        <?php echo app('translator')->get('Get'); ?> - <?php echo e($appliedCoupon->coupon_type == 0 ? showAmount($appliedCoupon->coupon_discount) . '%' : showAmount($cart->sum('discount')) . ' ' . $general->cur_text); ?> <?php echo app('translator')->get('Discount'); ?></span>
                                    <span>
                                        <?php echo e(@$general->cur_sym); ?><span class="discountAmounts"><?php echo e(showAmount($cart->sum('discount'))); ?></span> <?php echo e(__(@$general->cur_text)); ?>

                                    </span>
                                </div>
                            </div>
                        <?php endif; ?>
                        <div class="d-flex flex-wrap justify-content-between border-top mt-2 pt-4">
                            <h5 class="text-center fw-bold"><?php echo app('translator')->get('Total'); ?></h5>
                            <h5 class="justify-content-end d-flex">
                                <?php echo e(@$general->cur_sym); ?><span class="finalAmounts"><?php echo e(showAmount($carts->sum('after_discount'))); ?></span>
                                <?php echo e(__(@$general->cur_text)); ?>

                            </h5>
                        </div>
                    </div>

                    <?php if(count($carts)): ?>
                        <div class="text-center mt-3">
                            <form action="<?php echo e(route('user.invoice.create')); ?>" method="post">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn bg--base btn-lg text-white w-100">
                                    <?php echo app('translator')->get('Checkout'); ?> <i class="la la-arrow-circle-right"></i>
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
    <style>
        .capitalize {
            text-transform: capitalize;
        }

        .cart_child:nth-child(odd) .card-body {
            background-color: #00000008;
        }

        @media only screen and (max-width: 767px) {
            .remove_cart {
                display: inline-block !important;
            }

            .remove_icon {
                display: none;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.side_bar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/templates/basic/user/cart.blade.php ENDPATH**/ ?>