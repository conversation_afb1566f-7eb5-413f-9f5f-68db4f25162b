{"name": "@webassemblyjs/helper-wasm-bytecode", "version": "1.11.1", "description": "WASM's Bytecode constants", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "gitHead": "3f07e2db2031afe0ce686630418c542938c1674b"}