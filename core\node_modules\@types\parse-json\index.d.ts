// Type definitions for parse-json 4.0
// Project: https://github.com/sindresorhus/parse-json
// Definitions by: mrmlnc <https://github.com/mrmlnc>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped

declare function parseJson(input: string | null, filepath?: string): any;
declare function parseJson(input: string | null, reviver: (key: any, value: any) => any, filepath?: string): any;

export = parseJson;
