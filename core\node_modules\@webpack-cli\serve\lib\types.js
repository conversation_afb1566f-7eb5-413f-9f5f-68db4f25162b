"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var hotOptionEnum;
(function (hotOptionEnum) {
    hotOptionEnum["only"] = "only";
})(hotOptionEnum || (hotOptionEnum = {}));
var hostEnum;
(function (hostEnum) {
    hostEnum["LocalIp"] = "local-ip";
    hostEnum["LocalIpv4"] = "local-ipv4";
    hostEnum["LocalIpv6"] = "local-ipv6";
})(hostEnum || (hostEnum = {}));
var allowedHostsEnum;
(function (allowedHostsEnum) {
    allowedHostsEnum["Auto"] = "auto";
    allowedHostsEnum["All"] = "all";
})(allowedHostsEnum || (allowedHostsEnum = {}));
var transportModeEnum;
(function (transportModeEnum) {
    transportModeEnum["SockJS"] = "sockjs";
    transportModeEnum["Ws"] = "ws";
})(transportModeEnum || (transportModeEnum = {}));
var devServerClientLogging;
(function (devServerClientLogging) {
    devServerClientLogging["none"] = "none";
    devServerClientLogging["error"] = "error";
    devServerClientLogging["warn"] = "warn";
    devServerClientLogging["info"] = "info";
    devServerClientLogging["log"] = "log";
    devServerClientLogging["verbose"] = "verbose";
})(devServerClientLogging || (devServerClientLogging = {}));
