<?php $__env->startPush('topBar'); ?>
    <ul class="nav nav-tabs mb-4 topTap breadcum-nav" role="tablist">
        <button class="breadcum-nav-close"><i class="las la-times"></i></button>
        <li class="nav-item" role="presentation">
            <a href="javascript:void(0)" class="nav-link" id="information-tab" data-bs-toggle="pill" data-bs-target="#information" type="button"
                role="tab" aria-controls="information" aria-selected="true"><?php echo app('translator')->get('Details'); ?>
            </a>
        </li>
        <li class="nav-item" role="presentation">
            <a href="javascript:void(0)" class="nav-link" id="edit-information-tab" data-bs-toggle="pill" data-bs-target="#edit-information"
                type="button" role="tab" aria-controls="edit-information" aria-selected="false"><?php echo app('translator')->get('Information'); ?>
            </a>
        </li>
        <li class="nav-item">
            <?php $hasPermission = App\Models\Role::hasPermission('admin.report.transaction')  ? 1 : 0;
            if($hasPermission == 1): ?>
                <a class="nav-link" href="<?php echo e(route('admin.report.transaction')); ?>?search=<?php echo e($user->username); ?>">
                    <?php echo app('translator')->get('Balance'); ?> (<?php echo e($general->cur_sym); ?><?php echo e(showAmount($user->balance)); ?>)
                </a>
            <?php else: ?> 
                <a class="nav-link disabled" href="javascript:void(0)">
                    <?php echo app('translator')->get('Balance'); ?> (<?php echo e($general->cur_sym); ?><?php echo e(showAmount($user->balance)); ?>)
                </a>
            <?php endif ?>
        </li>
        <li class="nav-item">
            <?php $hasPermission = App\Models\Role::hasPermission('admin.deposit.list')  ? 1 : 0;
            if($hasPermission == 1): ?>
                <a class="nav-link" href="<?php echo e(route('admin.deposit.list')); ?>?search=<?php echo e($user->username); ?>">
                    <?php echo app('translator')->get('Payments'); ?> (<?php echo e($general->cur_sym); ?><?php echo e(showAmount($totalDeposit)); ?>)
                </a>
            <?php else: ?> 
                <a class="nav-link disabled" href="javascript:void(0)">
                    <?php echo app('translator')->get('Payments'); ?> (<?php echo e($general->cur_sym); ?><?php echo e(showAmount($totalDeposit)); ?>)
                </a>
            <?php endif ?>
        </li>
        <li class="nav-item">
            <?php $hasPermission = App\Models\Role::hasPermission('admin.report.transaction')  ? 1 : 0;
            if($hasPermission == 1): ?>
                <a class="nav-link" href="<?php echo e(route('admin.report.transaction')); ?>?search=<?php echo e($user->username); ?>">
                    <?php echo app('translator')->get('Transactions'); ?> (<?php echo e($totalTransaction); ?>)
                </a>
            <?php else: ?> 
                <a class="nav-link disabled" href="javascript:void(0)">
                    <?php echo app('translator')->get('Transactions'); ?> (<?php echo e($totalTransaction); ?>)
                </a>
            <?php endif ?>
        </li>
        <li class="nav-item">
            <?php $hasPermission = App\Models\Role::hasPermission('admin.users.orders')  ? 1 : 0;
            if($hasPermission == 1): ?>
                <a class="nav-link" href="<?php echo e(route('admin.users.orders', $user->id)); ?>">
            <?php else: ?> 
                <a class="nav-link disabled" href="javascript:void(0)">
            <?php endif ?>
                <?php echo app('translator')->get('Orders'); ?> (<?php echo e(@$statistics['count_total_order']); ?>)
            </a>
        </li>
        <li class="nav-item">
            <?php $hasPermission = App\Models\Role::hasPermission('admin.users.invoices')  ? 1 : 0;
            if($hasPermission == 1): ?>
                <a class="nav-link" href="<?php echo e(route('admin.users.invoices', $user->id)); ?>">
            <?php else: ?> 
                <a class="nav-link disabled" href="javascript:void(0)">
            <?php endif ?>
                <?php echo app('translator')->get('Invoices'); ?> (<?php echo e(@$statistics['count_total_invoice']); ?>)
            </a>
        </li>
        <li class="nav-item"> 
            <?php $hasPermission = App\Models\Role::hasPermission('admin.users.cancellations')  ? 1 : 0;
            if($hasPermission == 1): ?>
                <a class="nav-link" href="<?php echo e(route('admin.users.cancellations', $user->id)); ?>">
            <?php else: ?> 
                <a class="nav-link disabled" href="javascript:void(0)">
            <?php endif ?>
                <?php echo app('translator')->get('Cancellations'); ?> (<?php echo e(@$statistics['count_total_cancellation']); ?>)
            </a>
        </li>
        <li class="nav-item">
            <?php $hasPermission = App\Models\Role::hasPermission('admin.users.services')  ? 1 : 0;
            if($hasPermission == 1): ?>
                <a class="nav-link" href="<?php echo e(route('admin.users.services', $user->id)); ?>">
            <?php else: ?> 
                <a class="nav-link disabled" href="javascript:void(0)">
            <?php endif ?>
                <?php echo app('translator')->get('Services'); ?> (<?php echo e(@$statistics['count_total_service']); ?>)
            </a>
        </li>
        <li class="nav-item">
            <?php $hasPermission = App\Models\Role::hasPermission('admin.users.domains')  ? 1 : 0;
            if($hasPermission == 1): ?>
                <a class="nav-link" href="<?php echo e(route('admin.users.domains', $user->id)); ?>">
            <?php else: ?> 
                <a class="nav-link disabled" href="javascript:void(0)">
            <?php endif ?>
                <?php echo app('translator')->get('Domains'); ?> (<?php echo e(@$statistics['count_total_domain']); ?>)
            </a>
        </li>
    </ul>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('panel'); ?>
    <div class="tab-content" id="pills-tabContent">
        <div class="tab-pane fade" id="information" role="tabpanel" aria-labelledby="information-tab"
            tabindex="0">
            <div class="row mt-30 justify-content-center">
                <div class="col-xl-6">
                    <div class="card-body p-0">
                        <div class="d-flex p-3 bg--primary align-items-center">
                            <div class="avatar avatar--lg">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="ps-3">
                                <h6 class="text--white"><?php echo e(__($user->fullname)); ?></h6>
                            </div>
                        </div>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('Name'); ?>
                                <span class="fw-bold"><?php echo e(__($user->fullname)); ?></span>
                            </li>
    
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('Username'); ?>
                                <span  class="fw-bold"><?php echo e($user->username); ?></span>
                            </li>
    
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('Email'); ?>
                                <span  class="fw-bold"><?php echo e($user->email); ?></span>
                            </li>
    
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('Mobile'); ?>
                                <span  class="font-weight-bold"><?php echo e($user->mobile); ?></span>
                            </li>
    
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('Address'); ?>
                                <span class="font-weight-bold"><?php echo e(@$user->address->address); ?></span>
                            </li>
    
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('State'); ?>
                                <span class="font-weight-bold"><?php echo e(@$user->address->state); ?></span>
                            </li>
    
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('Zip'); ?>
                                <span class="font-weight-bold"><?php echo e(@$user->address->zip); ?></span>
                            </li>
    
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('Country'); ?>
                                <span class="font-weight-bold"><?php echo e(@$user->address->country); ?></span>
                            </li>
    
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('City'); ?>
                                <span class="font-weight-bold"><?php echo e(@$user->address->city); ?></span>
                            </li>

                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('Email Verification'); ?>
                                <?php if($user->ev): ?>
                                    <span class="badge badge--success"><?php echo app('translator')->get('Verified'); ?></span>
                                <?php else: ?>
                                    <span class="badge badge--warning"><?php echo app('translator')->get('Unverified'); ?></span>
                                <?php endif; ?>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('Mobile Verification'); ?>
                                <?php if($user->sv): ?>
                                    <span class="badge badge--success"><?php echo app('translator')->get('Verified'); ?></span>
                                <?php else: ?>
                                    <span class="badge badge--warning"><?php echo app('translator')->get('Unverified'); ?></span>
                                <?php endif; ?>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('2FA Verification'); ?>
                                <?php if($user->ts): ?>
                                    <span class="badge badge--success"><?php echo app('translator')->get('Enabled'); ?></span>
                                <?php else: ?>
                                    <span class="badge badge--warning"><?php echo app('translator')->get('Disabled'); ?></span>
                                <?php endif; ?>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <?php echo app('translator')->get('KYC Status'); ?>
                                <?php if($user->kv == 0): ?>
                                    <span class="badge badge--warning"><?php echo app('translator')->get('Unverified'); ?></span>
                                <?php elseif($user->kv == 1): ?>
                                    <span class="badge badge--success"><?php echo app('translator')->get('Verified'); ?></span>
                                <?php else: ?> 
                                    <span class="badge badge--danger"><?php echo app('translator')->get('Pending'); ?></span>
                                <?php endif; ?>
                            </li>
    
                        </ul>
                    </div>
                </div>
                <div class="col-xl-6 mt-4 mt-xl-0">
                    <div class="d-flex flex-wrap gap-3">

                        <?php $hasPermission = App\Models\Role::hasPermission('admin.users.add.sub.balance')  ? 1 : 0;
            if($hasPermission == 1): ?>
                            <div class="flex-fill">
                                <button data-bs-toggle="modal" data-bs-target="#addSubModal" class="btn btn--success btn--shadow w-100 btn-lg bal-btn" data-act="add">
                                    <i class="las la-plus-circle"></i> <?php echo app('translator')->get('Balance'); ?>
                                </button>
                            </div>
                            <div class="flex-fill">
                                <button data-bs-toggle="modal" data-bs-target="#addSubModal" class="btn btn--danger btn--shadow w-100 btn-lg bal-btn" data-act="sub">
                                    <i class="las la-minus-circle"></i> <?php echo app('translator')->get('Balance'); ?>
                                </button>
                            </div>
                        <?php endif ?>
            
                        <div class="flex-fill">
                            <?php $hasPermission = App\Models\Role::hasPermission('admin.report.login.history')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                <a href="<?php echo e(route('admin.report.login.history')); ?>?search=<?php echo e($user->username); ?>" class="btn btn--primary btn--shadow w-100 btn-lg">
                                    <i class="las la-list-alt"></i><?php echo app('translator')->get('Logins'); ?>
                                </a>
                            <?php else: ?> 
                                <a href="javascript:void(0)" class="btn btn--primary btn--shadow w-100 btn-lg">
                                    <i class="las la-list-alt"></i><?php echo app('translator')->get('Logins'); ?>
                                </a>
                            <?php endif ?>
                        </div>
            
                        <?php $hasPermission = App\Models\Role::hasPermission('admin.users.notification.log')  ? 1 : 0;
            if($hasPermission == 1): ?>
                            <div class="flex-fill">
                                <a href="<?php echo e(route('admin.users.notification.log',$user->id)); ?>" class="btn btn--secondary btn--shadow w-100 btn-lg">
                                    <i class="las la-bell"></i><?php echo app('translator')->get('Notifications'); ?>
                                </a>
                            </div>
                        <?php endif ?>
            
                        <?php $hasPermission = App\Models\Role::hasPermission('admin.users.login')  ? 1 : 0;
            if($hasPermission == 1): ?>
                            <div class="flex-fill">
                                <a href="<?php echo e(route('admin.users.login',$user->id)); ?>" target="_blank" class="btn btn--primary btn--gradi btn--shadow w-100 btn-lg">
                                    <i class="las la-sign-in-alt"></i><?php echo app('translator')->get('Login as Client'); ?>
                                </a>
                            </div>
                        <?php endif ?>
            
                        <?php $hasPermission = App\Models\Role::hasPermission('admin.users.kyc.details')  ? 1 : 0;
            if($hasPermission == 1): ?>
                            <?php if($user->kyc_data): ?>
                                <div class="flex-fill">
                                    <a href="<?php echo e(route('admin.users.kyc.details', $user->id)); ?>" target="_blank" class="btn btn--dark btn--shadow w-100 btn-lg">
                                        <i class="las la-user-check"></i><?php echo app('translator')->get('KYC Data'); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endif ?>
            
                        <?php $hasPermission = App\Models\Role::hasPermission('admin.users.status')  ? 1 : 0;
            if($hasPermission == 1): ?>
                            <div class="flex-fill">
                                <?php if($user->status == 1): ?>
                                <button type="button" class="btn btn--warning btn--gradi btn--shadow w-100 btn-lg userStatus" data-bs-toggle="modal" data-bs-target="#userStatusModal">
                                    <i class="las la-ban"></i><?php echo app('translator')->get('Ban User'); ?>
                                </button>
                                <?php else: ?>
                                <button type="button" class="btn btn--success btn--gradi btn--shadow w-100 btn-lg userStatus" data-bs-toggle="modal" data-bs-target="#userStatusModal">
                                    <i class="las la-undo"></i><?php echo app('translator')->get('Unban User'); ?>
                                </button>
                                <?php endif; ?>
                            </div>
                        <?php endif ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-pane fade" id="edit-information" role="tabpanel" aria-labelledby="edit-information-tab" tabindex="0">
            <div class="card mt-30">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo app('translator')->get('Edit Information of'); ?> <?php echo e($user->fullname); ?></h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.users.update',[$user->id])); ?>" method="POST" enctype="multipart/form-data" class="form">
                        <?php echo csrf_field(); ?>
        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group ">
                                    <label><?php echo app('translator')->get('First Name'); ?></label>
                                    <input class="form-control" type="text" name="firstname" required value="<?php echo e($user->firstname); ?>">
                                </div>
                            </div>
        
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-control-label"><?php echo app('translator')->get('Last Name'); ?></label>
                                    <input class="form-control" type="text" name="lastname" required value="<?php echo e($user->lastname); ?>">
                                </div>
                            </div>
                        </div>
        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Email'); ?> </label>
                                    <input class="form-control" type="email" name="email" value="<?php echo e($user->email); ?>" required>
                                </div>
                            </div>
        
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Mobile Number'); ?> </label>
                                    <div class="input-group ">
                                        <span class="input-group-text mobile-code"></span>
                                        <input type="number" name="mobile" value="<?php echo e(old('mobile')); ?>" id="mobile" class="form-control checkUser" required>
                                    </div>
                                </div>
                            </div>
                        </div>
        
        
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="form-group ">
                                    <label><?php echo app('translator')->get('Address'); ?></label>
                                    <input class="form-control" type="text" name="address" value="<?php echo e(@$user->address->address); ?>">
                                </div>
                            </div>
        
                            <div class="col-xl-3 col-md-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('City'); ?></label>
                                    <input class="form-control" type="text" name="city" value="<?php echo e(@$user->address->city); ?>">
                                </div>
                            </div>
        
                            <div class="col-xl-3 col-md-6">
                                <div class="form-group ">
                                    <label><?php echo app('translator')->get('State'); ?></label>
                                    <input class="form-control" type="text" name="state" value="<?php echo e(@$user->address->state); ?>">
                                </div>
                            </div>
        
                            <div class="col-xl-3 col-md-6">
                                <div class="form-group ">
                                    <label><?php echo app('translator')->get('Zip/Postal'); ?></label>
                                    <input class="form-control" type="text" name="zip" value="<?php echo e(@$user->address->zip); ?>">
                                </div>
                            </div>
        
                            <div class="col-xl-3 col-md-6">
                                <div class="form-group ">
                                    <label><?php echo app('translator')->get('Country'); ?></label>
                                    <select name="country" class="form-control">
                                        <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option data-mobile_code="<?php echo e($country->dial_code); ?>" value="<?php echo e($key); ?>"><?php echo e(__($country->country)); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                        </div>
        
        
                        <div class="row">
                            <div class="form-group  col-xl-3 col-md-6 col-12">
                                <label><?php echo app('translator')->get('Email Verification'); ?></label>
                                <input type="checkbox" data-width="100%" data-onstyle="-success" data-offstyle="-danger"
                                        data-bs-toggle="toggle" data-on="<?php echo app('translator')->get('Verified'); ?>" data-off="<?php echo app('translator')->get('Unverified'); ?>" name="ev"
                                        <?php if($user->ev): ?> checked <?php endif; ?>>
        
                            </div>
        
                            <div class="form-group  col-xl-3 col-md-6 col-12">
                                <label><?php echo app('translator')->get('Mobile Verification'); ?></label>
                                <input type="checkbox" data-width="100%" data-onstyle="-success" data-offstyle="-danger"
                                        data-bs-toggle="toggle" data-on="<?php echo app('translator')->get('Verified'); ?>" data-off="<?php echo app('translator')->get('Unverified'); ?>" name="sv"
                                        <?php if($user->sv): ?> checked <?php endif; ?>>
        
                            </div>
                            <div class="form-group col-xl-3 col-md- col-12">
                                <label><?php echo app('translator')->get('2FA Verification'); ?> </label>
                                <input type="checkbox" data-width="100%" data-height="50" data-onstyle="-success" data-offstyle="-danger" data-bs-toggle="toggle" data-on="<?php echo app('translator')->get('Enable'); ?>" data-off="<?php echo app('translator')->get('Disable'); ?>" name="ts" <?php if($user->ts): ?> checked <?php endif; ?>>
                            </div>
                            <div class="form-group col-xl-3 col-md- col-12">
                                <label><?php echo app('translator')->get('KYC'); ?> </label>
                                <input type="checkbox" data-width="100%" data-height="50" data-onstyle="-success" data-offstyle="-danger" data-bs-toggle="toggle" data-on="<?php echo app('translator')->get('Verified'); ?>" data-off="<?php echo app('translator')->get('Unverified'); ?>" name="kv" <?php if($user->kv == 1): ?> checked <?php endif; ?>>
                            </div>
                        </div>
        
                        <?php $hasPermission = App\Models\Role::hasPermission('admin.users.update')  ? 1 : 0;
            if($hasPermission == 1): ?>
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <button type="submit" class="btn btn--primary w-100 h-45">
                                            <?php echo app('translator')->get('Submit'); ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endif ?>
                    </form>
                </div>
            </div>
        </div>
    </div>

    
    <div id="addSubModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><span class="type"></span> <span><?php echo app('translator')->get('Balance'); ?></span></h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </button>
                </div>
                <form action="<?php echo e(route('admin.users.add.sub.balance', $user->id)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="act">
                    <div class="modal-body">
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Amount'); ?></label>
                            <div class="input-group">
                                <input type="number" step="any" name="amount" class="form-control"
                                    placeholder="<?php echo app('translator')->get('Please provide positive amount'); ?>" required>
                                <div class="input-group-text"><?php echo e(__($general->cur_text)); ?></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Remark'); ?></label>
                            <textarea class="form-control" placeholder="<?php echo app('translator')->get('Remark'); ?>" name="remark" rows="4" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn--primary h-45 w-100"><?php echo app('translator')->get('Submit'); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <div id="userStatusModal" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <?php if($user->status == 1): ?>
                            <span><?php echo app('translator')->get('Ban User'); ?></span>
                        <?php else: ?>
                            <span><?php echo app('translator')->get('Unban User'); ?></span>
                        <?php endif; ?>
                    </h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="las la-times"></i>
                    </button>
                </div>
                <form action="<?php echo e(route('admin.users.status', $user->id)); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <?php if($user->status == 1): ?>
                            <h6 class="mb-2"><?php echo app('translator')->get('If you ban this user he/she won\'t able to access his/her dashboard.'); ?></h6>
                            <div class="form-group">
                                <label><?php echo app('translator')->get('Reason'); ?></label>
                                <textarea class="form-control" name="reason" rows="4" required></textarea>
                            </div>
                        <?php else: ?>
                            <p><span><?php echo app('translator')->get('Ban reason was'); ?>:</span></p>
                            <p><?php echo e($user->ban_reason); ?></p>
                            <h4 class="text-center mt-3"><?php echo app('translator')->get('Are you sure to unban this user?'); ?></h4>
                        <?php endif; ?>
                    </div>
                    <div class="modal-footer">
                        <?php if($user->status == 1): ?>
                            <button type="submit" class="btn btn--primary h-45 w-100"><?php echo app('translator')->get('Submit'); ?></button>
                        <?php else: ?>
                            <button type="button" class="btn btn--dark"
                                data-bs-dismiss="modal"><?php echo app('translator')->get('No'); ?></button>
                            <button type="submit" class="btn btn--primary"><?php echo app('translator')->get('Yes'); ?></button>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        (function($) {
            "use strict"
            $('.bal-btn').click(function() {
                var act = $(this).data('act');
                $('#addSubModal').find('input[name=act]').val(act);
                if (act == 'add') {
                    $('.type').text('Add');
                } else {
                    $('.type').text('Subtract');
                }
            });
            let mobileElement = $('.mobile-code');
            $('select[name=country]').change(function() {
                mobileElement.text(`+${$('select[name=country] :selected').data('mobile_code')}`);
            });

            $('select[name=country]').val('<?php echo e(@$user->country_code); ?>');
            let dialCode = $('select[name=country] :selected').data('mobile_code');
            let mobileNumber = `<?php echo e($user->mobile); ?>`;
            mobileNumber = mobileNumber.replace(dialCode, '');
            $('input[name=mobile]').val(mobileNumber);
            mobileElement.text(`+${dialCode}`);

            $('.form').on('submit', function(){
                localStorage.setItem('isFormSubmit', true);
            });

            if(localStorage.getItem('isFormSubmit')){

                $('#edit-information-tab').addClass('active');
                $('#edit-information').addClass('active show');

                localStorage.removeItem('isFormSubmit');
            }else{
                $('#information-tab').addClass('active');
                $('#information').addClass('active show');
            }

            $('.breadcum-nav-open').on('click', function(){
                $(this).toggleClass('active');
                $('.breadcum-nav').toggleClass('active');
            });

            $('.breadcum-nav-close').on('click', function(){
                $('.breadcum-nav').removeClass('active');
            });

        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('style'); ?>
<style>
    @media (max-width: 1299px){
        .breadcum-nav {
            margin: 0;
            position: fixed;
            top: 0;
            right: -390px;
            width: 300px;
            background-color: #fff;
            display: block;
            min-height: 100vh;
            z-index: 99;
            border: none;
            box-shadow: -5px 0 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
        }
        .breadcum-nav-open {
            display: inline-block;
        }
        .breadcum-nav.active {
            right: 0; 
        }
        .breadcum-nav li {
            margin-top: 0;
        }
        .breadcum-nav li .nav-link {
            border: 0;
        }
       
    }
    @media (max-width: 1699px){
        .breadcum-nav li a {
            padding: 12px;
            font-size: 13px;
        }
    }
    @media (max-width: 1499px){
        .breadcum-nav li a {
            padding: 12px 7px;
            font-size: 12px;
        }
    }
    @media (max-width: 380px){
        .breadcum-nav {
            width: 220px;
        }
    }
    .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active{
        background-color: #f3f3f9;
        border-color: #dee2e6 #dee2e6 #f3f3f9;
    }
</style>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/users/detail.blade.php ENDPATH**/ ?>