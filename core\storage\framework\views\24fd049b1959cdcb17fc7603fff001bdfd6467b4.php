

<?php $__env->startSection('panel'); ?>
<div class="row">
    <div class="col-lg-12">
        <div class="card b-radius--10 ">
            <div class="card-body p-0">
                <div class="table-responsive--md  table-responsive">
                    <table class="table table--light style--two">
                        <thead> 
                        <tr>
                            <th><?php echo app('translator')->get('Service Provider'); ?></th>
                            <th><?php echo app('translator')->get('Test Mode'); ?></th>
                            <th><?php echo app('translator')->get('Status'); ?></th>
                            <th><?php echo app('translator')->get('Default'); ?></th>
                            <th><?php echo app('translator')->get('Action'); ?></th>
                        </tr>
                        </thead>
                        <tbody>  
                            <?php $__empty_1 = true; $__currentLoopData = $domainRegisters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <span class="fw-bold"><?php echo e($data->name); ?></span>
                                    </td>
                                    <td>
                                        <?php echo $data->showTestMode; ?>
                                    </td>
                                    <td>
                                        <?php echo $data->showStatus; ?>
                                    </td>
                                    <td>
                                        <?php echo $data->showDefault; ?>
                                    </td>
                                    <td>
                                        <div class="button--group">
                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.register.domain.update')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <button class="btn btn-sm btn-outline--primary configBtn" data-data="<?php echo e($data); ?>"> 
                                                    <i class="las la-cogs"></i> <?php echo app('translator')->get('Config'); ?>
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-sm btn-outline--primary" disabled> 
                                                    <i class="las la-cogs"></i> <?php echo app('translator')->get('Config'); ?>
                                                </button>
                                            <?php endif ?>
                                            
                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.register.domain.status')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <?php if($data->status == 0): ?>
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline--success confirmationBtn"
                                                            data-action="<?php echo e(route('admin.register.domain.status', $data->id)); ?>"
                                                            data-question="<?php echo app('translator')->get('Are you sure to enable this domain register?'); ?>">
                                                        <i class="la la-eye"></i> <?php echo app('translator')->get('Enable'); ?>
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-outline--danger confirmationBtn"
                                                    data-action="<?php echo e(route('admin.register.domain.status', $data->id)); ?>"
                                                    data-question="<?php echo app('translator')->get('Are you sure to disable this domain register?'); ?>">
                                                            <i class="la la-eye-slash"></i> <?php echo app('translator')->get('Disable'); ?>
                                                    </button>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <?php if($data->status == 0): ?>
                                                    <button type="button" class="btn btn-sm btn-outline--success" disabled>
                                                        <i class="la la-eye"></i> <?php echo app('translator')->get('Enable'); ?>
                                                    </button>
                                                <?php else: ?>
                                                    <button type="button" class="btn btn-sm btn-outline--danger" disabled>
                                                        <i class="la la-eye-slash"></i> <?php echo app('translator')->get('Disable'); ?>
                                                    </button>
                                                <?php endif; ?>
                                            <?php endif ?>
                                        </div>
                                    </td> 
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage)); ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table><!-- table end -->
                </div>
            </div>
            <?php if($domainRegisters->hasPages()): ?>
                <div class="card-footer py-4">
                    <?php echo e(paginateLinks($domainRegisters)); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if (isset($component)) { $__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b = $component; } ?>
<?php $component = App\View\Components\ConfirmationModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('confirmation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\ConfirmationModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b)): ?>
<?php $component = $__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b; ?>
<?php unset($__componentOriginalc51724be1d1b72c3a09523edef6afdd790effb8b); ?>
<?php endif; ?>


<div class="modal fade" id="configModal" tabindex="-1" role="dialog" aria-labelledby="autoRegisterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo app('translator')->get('Update Configuration'); ?>: <span class="provider-name"></span></h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="las la-times"></i>
                </button>
            </div> 
            <form class="form-horizontal" method="post" action="<?php echo e(route('admin.register.domain.update')); ?>">
                <?php echo csrf_field(); ?> 
                <input type="hidden" name="id" required>
                <div class="modal-body">
                    <div class="row">
                        <div class="configFields w-100"></div> 
                        <div class="col-md-12">
                            <div class="form-group">
                                <input type="checkbox" name="test_mode" id="test_mode">
                                <label for="test_mode"><?php echo app('translator')->get('Test Mode'); ?></label>
                            </div>
                        </div>

                        <div class="border-line-area style-two">
                            <h5 class="border-line-title"><?php echo app('translator')->get('Nameservers'); ?></h5>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="test_mode"><?php echo app('translator')->get('Default Nameserver 1'); ?></label>
                                <input class="form-control form-control-lg" type="text" name="ns1" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="test_mode"><?php echo app('translator')->get('Default Nameserver 2'); ?></label>
                                <input class="form-control form-control-lg" type="text" name="ns2" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="test_mode"><?php echo app('translator')->get('Default Nameserver 3'); ?></label>
                                <input class="form-control form-control-lg" type="text" name="ns3">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="test_mode"><?php echo app('translator')->get('Default Nameserver 4'); ?></label>
                                <input class="form-control form-control-lg" type="text" name="ns4">
                            </div>
                        </div>
                        
                        <div class="col-md-12"> 
                            <div class="form-group">
                                <label><?php echo app('translator')->get('Default Domain Register'); ?></label>
                                <input type="checkbox" data-width="100%" data-size="large" data-onstyle="-success" data-offstyle="-danger" data-toggle="toggle" data-on="<?php echo app('translator')->get('Default'); ?>" data-off="<?php echo app('translator')->get('Unset'); ?>" name="default">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn--primary h-45 w-100"><?php echo app('translator')->get('Submit'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>


<div id="autoRegisterModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo app('translator')->get('Confirmation Alert!'); ?></h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="las la-times"></i>
                </button> 
            </div>
            <form class="form-horizontal" method="post" action="<?php echo e(route('admin.register.domain.auto')); ?>">
                <?php echo csrf_field(); ?> 
                <div class="modal-body"> 
                    <?php if($general->auto_domain_register): ?>
                        <p class="question"><?php echo app('translator')->get('Are you sure to disable auto domain register'); ?></p>
                    <?php else: ?> 
                        <p class="question"><?php echo app('translator')->get('Are you sure to enable auto domain register'); ?></p>
                    <?php endif; ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn--dark" data-bs-dismiss="modal"><?php echo app('translator')->get('No'); ?></button>
                    <button type="submit" class="btn btn--primary"><?php echo app('translator')->get('Yes'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $hasPermission = App\Models\Role::hasPermission('admin.register.domain.auto')  ? 1 : 0;
            if($hasPermission == 1): ?>
    <?php $__env->startPush('breadcrumb-plugins'); ?>
        <?php if($general->auto_domain_register): ?>
            <button class="btn btn-sm btn-outline--danger autoRegister">
                <i class="las la-ban"></i><?php echo app('translator')->get('Disable Auto Domain Register'); ?>
            </button>
        <?php else: ?> 
            <button class="btn btn-sm btn-outline--primary autoRegister">
                <i class="las la-check"></i><?php echo app('translator')->get('Enable Auto Domain Register'); ?>
            </button>
        <?php endif; ?>
    <?php $__env->stopPush(); ?>
<?php endif ?>

<?php $__env->startPush('script'); ?>
    <script>
        (function($){
            "use strict";  
 
            $('.autoRegister').on('click', function () {
                var modal = $('#autoRegisterModal');
                modal.modal('show');
            });

            $('.configBtn').on('click', function () {
                var modal = $('#configModal'); 
                var data = $(this).data('data');
                var appendArea = modal.find('.configFields');
                appendArea.empty();

                $('input[name=ns1]').val(data.ns1);
                $('input[name=ns2]').val(data.ns2);
                $('input[name=ns3]').val(data.ns3);
                $('input[name=ns4]').val(data.ns4);

                modal.find('.provider-name').text(data.name);
                modal.find('input[name=id]').val(data.id);

                if(data.test_mode == 1){
                    modal.find('input[name=test_mode]').prop('checked', true);
                }else{
                    modal.find('input[name=test_mode]').prop('checked', false);
                }

                if(data.default == 1){
                    modal.find('input[name=default]').bootstrapToggle('on');
                }else{
                    modal.find('input[name=default]').bootstrapToggle('off');
                }

                for(var [key, item] of Object.entries(data.params)){
                    appendArea.append(`
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class='capitalize'>${item.title}</label>
                                <input type='text' class='form-control' placeholder='${key}' value='${item.value}' name='${key}'>
                            </div>
                        </div>
                    `);
                }

                modal.modal('show');
            });

        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>






<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/tld/register.blade.php ENDPATH**/ ?>