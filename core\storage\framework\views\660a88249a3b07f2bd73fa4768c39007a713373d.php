<?php $__env->startSection('panel'); ?>
    <div class="row">
        <div class="col-lg-12">
            <div class="card b-radius--10 ">
                <div class="card-body p-0">
                    <div class="table-responsive--md  table-responsive">
                        <table class="table table--light style--two">
                            <thead>
                            <tr>
                                <th><?php echo app('translator')->get('Invoice'); ?></th>
                                <th><?php echo app('translator')->get('User'); ?></th>
                                <th><?php echo app('translator')->get('Date'); ?></th>
                                <th><?php echo app('translator')->get('Total'); ?> | <?php echo app('translator')->get('Status'); ?></th>
                                <th><?php echo app('translator')->get('Action'); ?></th>
                            </tr>
                            </thead>
                            <tbody> 
                            <?php $__empty_1 = true; $__currentLoopData = @$invoices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $invoice): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <span class="fw-bold">
                                            <?php $hasPermission = App\Models\Role::hasPermission('admin.invoices.details')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                                <a href="<?php echo e(route('admin.invoices.details', @$invoice->id)); ?>">
                                                    <?php echo e(@$invoice->getInvoiceNumber); ?>

                                                </a> 
                                            <?php else: ?>
                                                <a href="javascript:void(0)">
                                                    <?php echo e(@$invoice->getInvoiceNumber); ?>

                                                </a> 
                                            <?php endif ?>
                                        </span>
                                    </td> 

                                    <td>
                                         <span class="fw-bold"><?php echo e(@$invoice->user->fullname); ?></span>
                                        <br>
                                        <span class="small">
                                            <a href="<?php echo e(permit('admin.users.detail') ? route('admin.users.detail', @$invoice->user->id) : 'javascript:void(0)'); ?>">
                                                <span>@</span><?php echo e(@$invoice->user->username); ?>

                                            </a>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo e(showDateTime(@$invoice->created_at)); ?> <br> <?php echo e(diffForHumans(@$invoice->created_at)); ?>

                                    </td> 

                                    <td>
                                        <span class="fw-bold"> 
                                            <?php echo e($general->cur_sym); ?><?php echo e(showAmount(@$invoice->amount)); ?></a>
                                        </span>
                                        <br>
                                        <?php echo $invoice->showStatus; ?>
                                    </td>
                                
                                    <td>
                                        <?php $hasPermission = App\Models\Role::hasPermission('admin.invoices.details')  ? 1 : 0;
            if($hasPermission == 1): ?>
                                            <a class="btn btn-sm btn-outline--primary" href="<?php echo e(route('admin.invoices.details', @$invoice->id)); ?>">
                                                <i class="la la-desktop"></i> <?php echo app('translator')->get('Details'); ?>
                                            </a>
                                        <?php else: ?> 
                                            <button class="btn btn-sm btn-outline--primary" disabled>
                                                <i class="la la-desktop"></i> <?php echo app('translator')->get('Details'); ?>
                                            </button>
                                        <?php endif ?>
                                    </td> 
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td class="text-muted text-center" colspan="100%"><?php echo e(__($emptyMessage)); ?></td>
                                </tr> 
                            <?php endif; ?>
                            </tbody>
                        </table><!-- table end -->
                    </div>
                </div>
                <?php if($invoices->hasPages()): ?>
                    <div class="card-footer py-4">
                        <?php echo e(paginateLinks($invoices)); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('breadcrumb-plugins'); ?>
<?php if(@$hosting): ?>
    <?php $hasPermission = App\Models\Role::hasPermission('admin.order.hosting.details')  ? 1 : 0;
            if($hasPermission == 1): ?>
        <a href="<?php echo e(route('admin.order.hosting.details', @$hosting->id)); ?>" class="btn btn-sm btn-outline--primary me-1">
            <i class="la la-undo"></i> <?php echo app('translator')->get('Go to Service'); ?>
        </a>
    <?php endif ?>
<?php endif; ?>
<?php if(@$domain): ?>
    <?php $hasPermission = App\Models\Role::hasPermission('admin.order.domain.details')  ? 1 : 0;
            if($hasPermission == 1): ?>
        <a href="<?php echo e(route('admin.order.domain.details', @$domain->id)); ?>" class="btn btn-sm btn-outline--primary me-1">
            <i class="la la-undo"></i> <?php echo app('translator')->get('Go to Domain'); ?>
        </a>
    <?php endif ?>
<?php endif; ?>

<?php if(!@$hosting && !@$domain && !@$user): ?>
<?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.search-form','data' => ['dateSearch' => 'yes','placeholder' => 'Username']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('search-form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['dateSearch' => 'yes','placeholder' => 'Username']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
<?php endif; ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/invoice/all.blade.php ENDPATH**/ ?>