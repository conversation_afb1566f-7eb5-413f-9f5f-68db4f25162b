<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'table' => false,
    'div' => false,
    'textAlign' => 'center',
    'colspan' => '100%',
    'message' => $emptyMessage,
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'table' => false,
    'div' => false,
    'textAlign' => 'center',
    'colspan' => '100%',
    'message' => $emptyMessage,
]); ?>
<?php foreach (array_filter(([
    'table' => false,
    'div' => false,
    'textAlign' => 'center',
    'colspan' => '100%',
    'message' => $emptyMessage,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php if($table): ?>
    <tr>
        <td class="text-<?php echo e($textAlign); ?> not-found" colspan="<?php echo e($colspan); ?>"><?php echo e(__($message)); ?></td>
    </tr>
<?php elseif($div): ?> 
    <div class="text-<?php echo e($textAlign); ?>">
        <?php echo e(__($message)); ?>

    </div>
<?php else: ?> 
    <?php echo e(__($message)); ?>

<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/components/empty-message.blade.php ENDPATH**/ ?>