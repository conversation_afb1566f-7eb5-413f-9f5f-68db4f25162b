<!-- navbar-wrapper start -->
<nav class="navbar-wrapper bg--dark">
    <div class="navbar__left">
        <button type="button" class="res-sidebar-open-btn me-3 flex-shrink-0"><i class="las la-bars"></i></button>
        <form class="navbar-search">
            <input type="search" name="#0" class="navbar-search-field" id="searchInput" autocomplete="off"
                placeholder="<?php echo app('translator')->get('Search here...'); ?>">
            <i class="las la-search"></i>
            <ul class="search-list"></ul>
        </form>
    </div>
    <div class="navbar__right">
        <ul class="navbar__action-list align-items-center">

            <li class="dropdown d-flex align-items-center gap-2">

                <?php $hasPermission = App\Models\Role::hasPermission('admin.domains')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <a href="<?php echo e(route('admin.domains')); ?>" class="primary--layer systemSetting" title="<?php echo app('translator')->get('Domains'); ?>" data-bs-placement="bottom">
                        <i class="las la-globe text--primary"></i>
                    </a> 
                <?php endif ?>
                <?php $hasPermission = App\Models\Role::hasPermission('admin.services')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <a href="<?php echo e(route('admin.services')); ?>" class="primary--layer systemSetting" title="<?php echo app('translator')->get('Services'); ?>" data-bs-placement="bottom">
                        <i class="las la-server text--primary"></i>
                    </a>
                <?php endif ?>
                <?php $hasPermission = App\Models\Role::hasPermission('admin.notifications')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <button type="button" class="primary--layer p-0" data-bs-toggle="dropdown" data-display="static"
                        aria-haspopup="true" aria-expanded="false">
                        <i class="las la-bell text--primary <?php if($adminNotificationCount > 0): ?> icon-left-right <?php endif; ?>"></i>
                    </button>
                <?php endif ?>
                <?php $hasPermission = App\Models\Role::hasPermission('admin.system.setting')  ? 1 : 0;
            if($hasPermission == 1): ?>
                    <a href="<?php echo e(route('admin.system.setting')); ?>" class="primary--layer systemSetting" title="<?php echo app('translator')->get('System Setting'); ?>" data-bs-placement="bottom">
                        <i class="las la-wrench text--primary"></i>
                    </a>  
                <?php endif ?>
                <div class="dropdown-menu dropdown-menu--md p-0 border-0 box--shadow1 dropdown-menu-right">
                    <div class="dropdown-menu__header">
                        <span class="caption"><?php echo app('translator')->get('Notification'); ?></span>
                        <?php if($adminNotificationCount > 0): ?>
                            <p><?php echo app('translator')->get('You have'); ?> <?php echo e($adminNotificationCount); ?> <?php echo app('translator')->get('unread notification'); ?></p>
                        <?php else: ?>
                            <p><?php echo app('translator')->get('No unread notification found'); ?></p>
                        <?php endif; ?>
                    </div>
                    <div class="dropdown-menu__body">
                        <?php $__currentLoopData = $adminNotifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e(permit('admin.notification.read') ? route('admin.notification.read',$notification->id) : 'javascript:void(0)'); ?>"
                                class="dropdown-menu__item">
                                <div class="navbar-notifi">
                                    <div class="navbar-notifi__right">
                                        <h6 class="notifi__title"><?php echo e(__($notification->title)); ?></h6>
                                        <span class="time"><?php echo e($notification->created_at->diffForHumans()); ?></span>
                                    </div>
                                </div><!-- navbar-notifi end -->
                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="dropdown-menu__footer">
                        <a href="<?php echo e(route('admin.notifications')); ?>"
                            class="view-all-message"><?php echo app('translator')->get('View all notification'); ?></a>
                    </div>
                </div>
            </li>

            <li class="d-flex flex-wrap align-items-center">
                <div class="dropdown">
                    <button type="button" class="" data-bs-toggle="dropdown" data-display="static" aria-haspopup="true"
                        aria-expanded="false">
                        <span class="navbar-user">
                            <span class="navbar-user__thumb"><img
                                    src="<?php echo e(getImage('assets/admin/images/profile/'. auth()->guard('admin')->user()->image)); ?>"
                                    alt="image"></span>
                            <span class="navbar-user__info">
                                <span
                                    class="navbar-user__name"><?php echo e(auth()->guard('admin')->user()->username); ?></span>
                            </span>
                            <span class="icon"><i class="las la-chevron-circle-down"></i></span>
                        </span>
                    </button>
                    <div class="dropdown-menu dropdown-menu--sm p-0 border-0 box--shadow1 dropdown-menu-right">
                        <a href="<?php echo e(route('admin.profile')); ?>"
                            class="dropdown-menu__item d-flex align-items-center px-3 py-2">
                            <i class="dropdown-menu__icon las la-user-circle"></i>
                            <span class="dropdown-menu__caption"><?php echo app('translator')->get('Profile'); ?></span>
                        </a>
    
                        <a href="<?php echo e(route('admin.password')); ?>"
                            class="dropdown-menu__item d-flex align-items-center px-3 py-2">
                            <i class="dropdown-menu__icon las la-key"></i>
                            <span class="dropdown-menu__caption"><?php echo app('translator')->get('Password'); ?></span>
                        </a>
    
                        <a href="<?php echo e(route('admin.logout')); ?>"
                            class="dropdown-menu__item d-flex align-items-center px-3 py-2 adminLogout">
                            <i class="dropdown-menu__icon las la-sign-out-alt"></i>
                            <span class="dropdown-menu__caption"><?php echo app('translator')->get('Logout'); ?></span>
                        </a>
                    </div>
                </div> 
    
                <button type="button" class="breadcum-nav-open ms-2 d-none">
                    <i class="las la-sliders-h"></i>
                </button>
            </li>            
        </ul>
    </div>
</nav>
<!-- navbar-wrapper end -->

<?php $__env->startPush('style'); ?>
    <style>
        @media (max-width: 767px) {
            .res-sidebar-open-btn {
                margin-right: 10px !important;
            }
        }
        @media (max-width: 611px) { 
            .navbar__right {
                margin-left: auto;
                width: 100%;
                margin-top: 8px;
            }
            .navbar__action-list {
                justify-content: space-between;
                width: 100%;
            }
            .navbar-user__info {
                display: block;
            }
            .navbar-user .icon {
                display: block;
            }
            .navbar__left {
                width: 100%;
            }
        }
        @media (max-width: 767px) {
            .navbar__right button i, .navbar__right a i {
                font-size: 20px
            }
        }
        .primary--layer {
            line-height: 1;
        }
    </style>
<?php $__env->stopPush(); ?><?php /**PATH C:\xampp\htdocs\achidas\core\resources\views/admin/partials/topnav.blade.php ENDPATH**/ ?>