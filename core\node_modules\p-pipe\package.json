{"name": "p-pipe", "version": "3.1.0", "description": "Compose promise-returning & async functions into a reusable pipeline", "license": "MIT", "repository": "sindresorhus/p-pipe", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "pipe", "pipeline", "compose", "composition", "combine", "flow", "serial", "functions", "reusable", "async", "await", "promises", "bluebird"], "devDependencies": {"ava": "^1.4.1", "sinon": "^7.3.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}